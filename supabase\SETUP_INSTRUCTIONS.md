# JARSS - Instrucciones de Configuración de Base de Datos

## 🚀 Configuración Inicial de Supabase

### Paso 1: Preparar el Entorno

1. **Copiar variables de entorno:**
   ```bash
   cp .env.example .env
   ```

2. **Configurar las variables en `.env`:**
   ```env
   VITE_SUPABASE_URL=https://xauoydmisqeexiztwmmu.supabase.co
   VITE_SUPABASE_ANON_KEY=tu_clave_anonima_aqui
   ```

### Paso 2: Ejecutar Migraciones

#### Opción A: Ejecutar todas las migraciones de una vez

1. Ir a Supabase Dashboard → SQL Editor
2. Copiar y pegar el contenido completo de cada archivo en orden:

```sql
-- 1. Ejecutar: supabase/migrations/001_initial_schema.sql
-- 2. Ejecutar: supabase/migrations/002_nomina_presupuestos.sql  
-- 3. Ejecutar: supabase/migrations/003_reportes_inversiones.sql
-- 4. Ejecutar: supabase/migrations/004_rls_policies.sql
-- 5. Ejecutar: supabase/migrations/005_functions_triggers.sql
-- 6. Ejecutar: supabase/migrations/006_seed_data.sql
```

#### Opción B: Usar CLI de Supabase (Recomendado)

1. **Instalar Supabase CLI:**
   ```bash
   npm install -g supabase
   ```

2. **Inicializar proyecto:**
   ```bash
   supabase init
   ```

3. **Conectar al proyecto:**
   ```bash
   supabase link --project-ref xauoydmisqeexiztwmmu
   ```

4. **Ejecutar migraciones:**
   ```bash
   supabase db push
   ```

### Paso 3: Verificar la Instalación

Ejecutar en SQL Editor para verificar que todo se instaló correctamente:

```sql
-- Verificar tablas creadas
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;

-- Verificar RLS habilitado
SELECT tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public' AND rowsecurity = true;

-- Verificar funciones creadas
SELECT routine_name 
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name LIKE '%dominican%' OR routine_name LIKE '%jarss%';
```

### Paso 4: Configurar Edge Functions

1. **Desplegar función JARSS API:**
   ```bash
   supabase functions deploy jarss-api
   ```

2. **Configurar variables de entorno para las funciones:**
   ```bash
   supabase secrets set SUPABASE_URL=https://xauoydmisqeexiztwmmu.supabase.co
   supabase secrets set SUPABASE_ANON_KEY=tu_clave_anonima
   ```

## 📊 Estructura de Datos Creada

### Tablas Principales:
- ✅ **profiles** - Perfiles de usuario
- ✅ **financial_accounts** - Cuentas financieras
- ✅ **financial_transactions** - Transacciones
- ✅ **tax_configurations** - Configuración fiscal dominicana
- ✅ **tax_obligations** - Obligaciones fiscales
- ✅ **employees** - Empleados
- ✅ **payroll_periods** - Períodos de nómina
- ✅ **payroll_entries** - Entradas de nómina
- ✅ **budgets** - Presupuestos
- ✅ **suppliers** - Proveedores
- ✅ **customers** - Clientes
- ✅ **investments** - Inversiones
- ✅ **financial_reports** - Reportes

### Configuraciones Automáticas:
- 🇩🇴 **Impuestos dominicanos** (ITBIS 18%, AFP 2.87%, SFS 3.04%, INFOTEP 1%)
- 📂 **Categorías por defecto** (ingresos y gastos)
- 💼 **Tipos de inversión**
- ⚙️ **Configuraciones del sistema**

### Funciones Especiales:
- 🧮 **calculate_dominican_deductions()** - Cálculo automático de deducciones
- 💰 **calculate_itbis()** - Cálculo de ITBIS
- 🔄 **update_account_balance()** - Actualización automática de balances
- 📊 **update_budget_variance()** - Cálculo de varianzas presupuestarias

## 🔐 Seguridad Implementada

### Row Level Security (RLS):
- ✅ Habilitado en todas las tablas
- ✅ Políticas que permiten acceso solo a datos propios
- ✅ Protección automática contra acceso no autorizado

### Auditoría:
- ✅ Registro automático de cambios en tablas críticas
- ✅ Logs de auditoría con timestamps y usuarios
- ✅ Trazabilidad completa de transacciones

## 🧪 Datos de Prueba (Opcional)

Para crear datos de ejemplo para desarrollo:

```sql
-- Ejecutar después de registrar un usuario
SELECT create_demo_data('tu-user-id-aqui');
```

Esto creará:
- Una cuenta bancaria de ejemplo
- Transacciones de muestra
- Presupuesto básico

## 🚨 Solución de Problemas

### Error: "relation does not exist"
- Verificar que todas las migraciones se ejecutaron en orden
- Revisar que no hay errores en los logs de Supabase

### Error: "permission denied"
- Verificar que RLS está configurado correctamente
- Confirmar que el usuario está autenticado

### Error: "function does not exist"
- Verificar que el archivo 005_functions_triggers.sql se ejecutó
- Revisar logs de errores en las funciones

### Rendimiento lento:
- Los índices se crean automáticamente
- Para grandes volúmenes de datos, considerar índices adicionales

## 📞 Soporte

Si encuentras problemas:

1. Revisar logs en Supabase Dashboard → Logs
2. Verificar configuración en Database → Settings
3. Consultar documentación en `DATABASE_SCHEMA.md`

## ✅ Lista de Verificación Post-Instalación

- [ ] Todas las migraciones ejecutadas sin errores
- [ ] RLS habilitado en todas las tablas
- [ ] Funciones y triggers creados
- [ ] Edge functions desplegadas
- [ ] Variables de entorno configuradas
- [ ] Datos iniciales creados para usuario de prueba
- [ ] Verificación de seguridad completada

¡Tu sistema JARSS está listo para usar! 🎉
