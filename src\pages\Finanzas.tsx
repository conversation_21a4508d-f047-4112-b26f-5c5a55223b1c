import { useState } from "react";
import { MainLayout } from "@/components/layout/MainLayout";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { StatCard } from "@/components/dashboard/StatCard";
import { Plus, Wallet, ArrowUp, ArrowDown, Receipt } from "lucide-react";
import { TransactionForm } from "@/components/finance/TransactionForm";
import { TransactionList } from "@/components/finance/TransactionList";
import { useAsync } from "@/hooks/useAsync";
import { financeService } from "@/services/finance/financeService";
import { FinancialSummary } from "@/types/finance";

const Finanzas = () => {
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const { execute: fetchSummary, data: summaryData, state: summaryState } = useAsync<FinancialSummary>();

  const handleTransactionCreated = () => {
    setRefreshTrigger(prev => prev + 1);
    fetchSummary(financeService.getFinancialSummary());
  };

  return (
    <MainLayout>
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">Gestión Financiera</h1>
          <Button className="gap-1">
            <Plus className="h-4 w-4" />
            <span>Nueva Transacción</span>
          </Button>
        </div>

        <div className="grid gap-4 md:grid-cols-4">
          <StatCard
            title="Balance Total"
            value={summaryData?.data?.totalBalance.toLocaleString('es-MX', { style: 'currency', currency: 'MXN' }) || '$0.00'}
            icon={Wallet}
            trend={summaryData?.data?.trends.balance > 0 ? 'up' : 'down'}
            trendValue={`${summaryData?.data?.trends.balance > 0 ? '+' : ''}${summaryData?.data?.trends.balance}% desde el mes pasado`}
            isLoading={summaryState === 'loading'}
          />
          <StatCard
            title="Ingresos (Mes)"
            value={summaryData?.data?.monthlyIncome.toLocaleString('es-MX', { style: 'currency', currency: 'MXN' }) || '$0.00'}
            icon={ArrowUp}
            trend={summaryData?.data?.trends.income > 0 ? 'up' : 'down'}
            trendValue={`${summaryData?.data?.trends.income > 0 ? '+' : ''}${summaryData?.data?.trends.income}% desde el mes pasado`}
            isLoading={summaryState === 'loading'}
          />
          <StatCard
            title="Gastos (Mes)"
            value={summaryData?.data?.monthlyExpenses.toLocaleString('es-MX', { style: 'currency', currency: 'MXN' }) || '$0.00'}
            icon={ArrowDown}
            trend={summaryData?.data?.trends.expenses < 0 ? 'up' : 'down'}
            trendValue={`${summaryData?.data?.trends.expenses > 0 ? '+' : ''}${summaryData?.data?.trends.expenses}% desde el mes pasado`}
            isLoading={summaryState === 'loading'}
          />
          <StatCard
            title="Cuentas por Cobrar"
            value={summaryData?.data?.accountsReceivable.toLocaleString('es-MX', { style: 'currency', currency: 'MXN' }) || '$0.00'}
            icon={Receipt}
            trend="neutral"
            trendValue="4 facturas pendientes"
            isLoading={summaryState === 'loading'}
          />
        </div>

        <Tabs defaultValue="transacciones">
          <TabsList className="grid w-full grid-cols-3 max-w-md">
            <TabsTrigger value="transacciones">Transacciones</TabsTrigger>
            <TabsTrigger value="cuentas">Cuentas</TabsTrigger>
            <TabsTrigger value="conciliacion">Conciliación</TabsTrigger>
          </TabsList>
          
          <TabsContent value="transacciones">
            <div className="grid md:grid-cols-3 gap-4">
              <TransactionForm onTransactionCreated={handleTransactionCreated} />
              <TransactionList refreshTrigger={refreshTrigger} />
            </div>
          </TabsContent>
          
          <TabsContent value="cuentas">
            <p className="text-center text-gray-500 py-8">
              La gestión de cuentas estará disponible próximamente
            </p>
          </TabsContent>
          
          <TabsContent value="conciliacion">
            <p className="text-center text-gray-500 py-8">
              La conciliación bancaria estará disponible próximamente
            </p>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
};

export default Finanzas;
