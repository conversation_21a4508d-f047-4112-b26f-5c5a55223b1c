
// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://xauoydmisqeexiztwmmu.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhhdW95ZG1pc3FlZXhpenR3bW11Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM1Mzk3NTksImV4cCI6MjA1OTExNTc1OX0.gI6b-L91620UeD4jZ5jDxcgTXf2M-kbdo8sAM_jIYo4";

// CSRF protection - Generate a token for requests
export const generateCSRFToken = () => {
  // Generate a random token
  const token = Math.random().toString(36).substring(2, 15) + 
                Math.random().toString(36).substring(2, 15);
  
  // Store the token in localStorage (in a real app, use HttpOnly cookies instead)
  localStorage.setItem('csrf_token', token);
  
  return token;
};

// XSS prevention - Sanitize content for display
export const sanitizeContent = (content: string): string => {
  if (!content) return '';
  
  return content
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#039;");
};

// SQL Injection prevention - Use parameterized queries
// This is automatically handled by the Supabase client, but we're adding a helper
// function to sanitize inputs for custom SQL or other uses
export const sanitizeInput = async (input: string): Promise<string> => {
  try {
    // Replace with basic client-side sanitization
    return input.replace(/[;'"\\]/g, '');
  } catch (error) {
    console.error('Error sanitizing input:', error);
    // Fallback client-side sanitization
    return input.replace(/[;'"\\]/g, '');
  }
};

// Configure Supabase client with additional security options
export const supabase = createClient<Database>(
  SUPABASE_URL, 
  SUPABASE_PUBLISHABLE_KEY,
  {
    auth: {
      persistSession: true,
      autoRefreshToken: true,
    },
    global: {
      // Add headers for each request
      headers: {
        'X-CSRF-Token': localStorage.getItem('csrf_token') || generateCSRFToken(),
      },
    },
  }
);

// Secure fetch wrapper for banking API calls
export const secureBankingFetch = async (operation: string, params: any) => {
  try {
    const csrfToken = localStorage.getItem('csrf_token') || generateCSRFToken();
    
    // Get the current user's auth token
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) throw new Error('Authentication required');
    
    const response = await supabase.functions.invoke('banking-api', {
      body: { operation, ...params },
      headers: {
        'X-CSRF-Token': csrfToken,
      },
    });
    
    if (response.error) throw new Error(response.error.message);
    return response.data;
  } catch (error) {
    console.error('Banking API error:', error);
    throw error;
  }
};
