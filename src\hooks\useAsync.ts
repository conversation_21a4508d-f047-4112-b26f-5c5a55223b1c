import { useState, useCallback } from 'react';
import { LoadingState } from '@/types/common';

interface UseAsyncOptions<T> {
  onSuccess?: (data: T) => void;
  onError?: (error: Error) => void;
}

export function useAsync<T>(options: UseAsyncOptions<T> = {}) {
  const [data, setData] = useState<T | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const [state, setState] = useState<LoadingState>('idle');

  const execute = useCallback(async (promise: Promise<T>) => {
    try {
      setState('loading');
      setError(null);
      
      const result = await promise;
      
      setData(result);
      setState('success');
      options.onSuccess?.(result);
      
      return result;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('An unexpected error occurred');
      setError(error);
      setState('error');
      options.onError?.(error);
      throw error;
    }
  }, [options]);

  const reset = useCallback(() => {
    setData(null);
    setError(null);
    setState('idle');
  }, []);

  return {
    data,
    error,
    state,
    execute,
    reset,
    isLoading: state === 'loading',
    isError: state === 'error',
    isSuccess: state === 'success',
    isIdle: state === 'idle',
  };
} 