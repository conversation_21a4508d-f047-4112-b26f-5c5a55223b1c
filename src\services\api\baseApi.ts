import { ApiError, ApiResponse } from '@/types/common';

const BASE_URL = import.meta.env.VITE_API_URL || '';

export class ApiErrorException extends Error {
  constructor(public error: ApiError) {
    super(error.message);
    this.name = 'ApiErrorException';
  }
}

export const handleApiError = (error: unknown): ApiError => {
  if (error instanceof ApiErrorException) {
    return error.error;
  }

  if (error instanceof Error) {
    return {
      code: 'UNKNOWN_ERROR',
      message: error.message
    };
  }

  return {
    code: 'UNKNOWN_ERROR',
    message: 'An unexpected error occurred'
  };
};

export const baseApi = {
  async get<T>(endpoint: string, params?: Record<string, string>): Promise<ApiResponse<T>> {
    try {
      const queryString = params ? `?${new URLSearchParams(params).toString()}` : '';
      const response = await fetch(`${BASE_URL}${endpoint}${queryString}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new ApiErrorException({
          code: `HTTP_${response.status}`,
          message: response.statusText,
        });
      }

      const data = await response.json();
      return { data, status: 'success' };
    } catch (error) {
      const apiError = handleApiError(error);
      return { data: null as T, error: apiError, status: 'error' };
    }
  },

  async post<T>(endpoint: string, body: unknown): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(`${BASE_URL}${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });

      if (!response.ok) {
        throw new ApiErrorException({
          code: `HTTP_${response.status}`,
          message: response.statusText,
        });
      }

      const data = await response.json();
      return { data, status: 'success' };
    } catch (error) {
      const apiError = handleApiError(error);
      return { data: null as T, error: apiError, status: 'error' };
    }
  },

  async put<T>(endpoint: string, body: unknown): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(`${BASE_URL}${endpoint}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });

      if (!response.ok) {
        throw new ApiErrorException({
          code: `HTTP_${response.status}`,
          message: response.statusText,
        });
      }

      const data = await response.json();
      return { data, status: 'success' };
    } catch (error) {
      const apiError = handleApiError(error);
      return { data: null as T, error: apiError, status: 'error' };
    }
  },

  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(`${BASE_URL}${endpoint}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new ApiErrorException({
          code: `HTTP_${response.status}`,
          message: response.statusText,
        });
      }

      const data = await response.json();
      return { data, status: 'success' };
    } catch (error) {
      const apiError = handleApiError(error);
      return { data: null as T, error: apiError, status: 'error' };
    }
  },
}; 