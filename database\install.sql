-- JARSS - Instalación Completa
-- Sistema Integral de Gestión Financiera
-- Base de datos PostgreSQL
-- Fecha: 2025-01-27

-- =====================================================
-- SCRIPT DE INSTALACIÓN COMPLETA
-- =====================================================

-- Este script instala toda la base de datos de JARSS
-- Ejecutar en PostgreSQL 12 o superior

\echo '🚀 Iniciando instalación de JARSS...'

-- Verificar versión de PostgreSQL
DO $$
DECLARE
    version_num integer;
BEGIN
    SELECT current_setting('server_version_num')::integer INTO version_num;
    IF version_num < 120000 THEN
        RAISE EXCEPTION 'JARSS requiere PostgreSQL 12 o superior. Versión actual: %', version();
    END IF;
    RAISE NOTICE '✅ PostgreSQL versión verificada: %', version();
END $$;

\echo '📊 Creando esquema de base de datos...'

-- Ejecutar esquema principal
\i schema.sql

\echo '⚙️ Creando funciones...'

-- Ejecutar funciones
\i functions.sql

\echo '🔄 Creando triggers...'

-- Ejecutar triggers
\i triggers.sql

\echo '📝 Insertando datos iniciales...'

-- Ejecutar datos iniciales
\i seed_data.sql

-- =====================================================
-- VERIFICACIÓN DE INSTALACIÓN
-- =====================================================

\echo '🔍 Verificando instalación...'

DO $$
DECLARE
    table_count INTEGER;
    function_count INTEGER;
    trigger_count INTEGER;
    expected_tables TEXT[] := ARRAY[
        'users', 'companies', 'company_users', 'financial_accounts', 
        'transaction_categories', 'financial_transactions', 'tax_configurations', 
        'tax_obligations', 'tax_declarations', 'departments', 'employees', 
        'payroll_periods', 'payroll_entries', 'budgets', 'budget_categories', 
        'budget_lines', 'suppliers', 'customers', 'accounts_payable', 
        'accounts_receivable', 'financial_reports', 'report_templates', 
        'investment_types', 'investments', 'investment_movements', 
        'system_configurations', 'notifications', 'audit_logs', 
        'bank_statements', 'bank_movements', 'recurring_payments', 
        'scheduled_payment_history', 'user_sessions'
    ];
    missing_tables TEXT[] := '{}';
    table_name TEXT;
BEGIN
    RAISE NOTICE '🔍 VERIFICANDO TABLAS...';
    
    -- Verificar que todas las tablas esperadas existen
    FOREACH table_name IN ARRAY expected_tables
    LOOP
        SELECT COUNT(*) INTO table_count
        FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_name = table_name;
        
        IF table_count = 0 THEN
            missing_tables := array_append(missing_tables, table_name);
        END IF;
    END LOOP;
    
    IF array_length(missing_tables, 1) > 0 THEN
        RAISE EXCEPTION '❌ TABLAS FALTANTES: %', array_to_string(missing_tables, ', ');
    ELSE
        RAISE NOTICE '✅ Todas las tablas creadas correctamente (%)', array_length(expected_tables, 1);
    END IF;
    
    -- Verificar funciones
    SELECT COUNT(*) INTO function_count
    FROM information_schema.routines 
    WHERE routine_schema = 'public' 
    AND routine_name IN (
        'update_updated_at_column', 'hash_password', 'verify_password',
        'calculate_dominican_deductions', 'calculate_itbis', 'update_account_balance',
        'audit_trigger', 'update_budget_variance', 'get_balance_sheet',
        'create_default_categories', 'create_default_tax_config',
        'setup_new_company', 'create_demo_data'
    );
    
    IF function_count < 10 THEN
        RAISE WARNING '⚠️ Pocas funciones encontradas (%)', function_count;
    ELSE
        RAISE NOTICE '✅ Funciones creadas correctamente (%)', function_count;
    END IF;
    
    -- Verificar triggers
    SELECT COUNT(*) INTO trigger_count
    FROM information_schema.triggers 
    WHERE trigger_schema = 'public';
    
    IF trigger_count < 20 THEN
        RAISE WARNING '⚠️ Pocos triggers encontrados (%)', trigger_count;
    ELSE
        RAISE NOTICE '✅ Triggers creados correctamente (%)', trigger_count;
    END IF;
END $$;

-- =====================================================
-- PRUEBAS BÁSICAS
-- =====================================================

\echo '🧪 Ejecutando pruebas básicas...'

DO $$
DECLARE
    test_result RECORD;
    test_company_id UUID;
    test_user_id UUID;
BEGIN
    RAISE NOTICE '🧪 EJECUTANDO PRUEBAS BÁSICAS...';
    
    -- Crear usuario de prueba
    INSERT INTO users (email, password_hash, full_name)
    VALUES ('<EMAIL>', hash_password('test123'), 'Usuario de Prueba')
    RETURNING id INTO test_user_id;
    
    -- Crear empresa de prueba
    INSERT INTO companies (owner_id, name, rnc)
    VALUES (test_user_id, 'Empresa de Prueba JARSS', '101-12345-6')
    RETURNING id INTO test_company_id;
    
    -- Configurar empresa
    PERFORM setup_new_company(test_company_id);
    
    -- Probar cálculo de deducciones dominicanas
    SELECT * INTO test_result FROM calculate_dominican_deductions(50000.00, test_company_id);
    
    IF test_result.afp_amount > 0 AND test_result.sfs_amount > 0 THEN
        RAISE NOTICE '✅ Función de deducciones dominicanas funciona correctamente';
        RAISE NOTICE '   AFP: RD$%, SFS: RD$%, INFOTEP: RD$%, ISR: RD$%', 
            test_result.afp_amount, test_result.sfs_amount, 
            test_result.infotep_amount, test_result.isr_amount;
    ELSE
        RAISE WARNING '⚠️ Problema con función de deducciones dominicanas';
    END IF;
    
    -- Probar cálculo de ITBIS
    IF calculate_itbis(1000.00, test_company_id, false) = 180.00 THEN
        RAISE NOTICE '✅ Función de cálculo ITBIS funciona correctamente';
    ELSE
        RAISE WARNING '⚠️ Problema con función de cálculo ITBIS';
    END IF;
    
    -- Verificar que se crearon datos iniciales
    SELECT COUNT(*) INTO test_result FROM transaction_categories WHERE company_id = test_company_id;
    IF test_result.count > 10 THEN
        RAISE NOTICE '✅ Categorías por defecto creadas (% categorías)', test_result.count;
    ELSE
        RAISE WARNING '⚠️ Pocas categorías creadas (%)', test_result.count;
    END IF;
    
    -- Limpiar datos de prueba
    DELETE FROM companies WHERE id = test_company_id;
    DELETE FROM users WHERE id = test_user_id;
    
    RAISE NOTICE '✅ Pruebas básicas completadas exitosamente';
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE WARNING '⚠️ Error en pruebas básicas: %', SQLERRM;
        -- Intentar limpiar en caso de error
        DELETE FROM companies WHERE owner_id = test_user_id;
        DELETE FROM users WHERE email = '<EMAIL>';
END $$;

-- =====================================================
-- ESTADÍSTICAS FINALES
-- =====================================================

DO $$
DECLARE
    table_count INTEGER;
    function_count INTEGER;
    trigger_count INTEGER;
    index_count INTEGER;
BEGIN
    -- Contar elementos creados
    SELECT COUNT(*) INTO table_count
    FROM information_schema.tables 
    WHERE table_schema = 'public' AND table_type = 'BASE TABLE';
    
    SELECT COUNT(*) INTO function_count
    FROM information_schema.routines 
    WHERE routine_schema = 'public';
    
    SELECT COUNT(*) INTO trigger_count
    FROM information_schema.triggers 
    WHERE trigger_schema = 'public';
    
    SELECT COUNT(*) INTO index_count
    FROM pg_indexes 
    WHERE schemaname = 'public';
    
    RAISE NOTICE '';
    RAISE NOTICE '🎉 ===== INSTALACIÓN COMPLETADA =====';
    RAISE NOTICE '';
    RAISE NOTICE '📊 ESTADÍSTICAS:';
    RAISE NOTICE '   • Tablas creadas: %', table_count;
    RAISE NOTICE '   • Funciones creadas: %', function_count;
    RAISE NOTICE '   • Triggers creados: %', trigger_count;
    RAISE NOTICE '   • Índices creados: %', index_count;
    RAISE NOTICE '';
    RAISE NOTICE '🇩🇴 CONFIGURACIÓN DOMINICANA:';
    RAISE NOTICE '   • ITBIS: 18%% (configurable)';
    RAISE NOTICE '   • AFP: 2.87%% (configurable)';
    RAISE NOTICE '   • SFS: 3.04%% (configurable)';
    RAISE NOTICE '   • INFOTEP: 1%% (configurable)';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 JARSS ESTÁ LISTO PARA USAR!';
    RAISE NOTICE '';
    RAISE NOTICE '📋 PRÓXIMOS PASOS:';
    RAISE NOTICE '   1. Crear usuario administrador';
    RAISE NOTICE '   2. Registrar empresa';
    RAISE NOTICE '   3. Configurar cuentas bancarias';
    RAISE NOTICE '   4. Comenzar a registrar transacciones';
    RAISE NOTICE '';
    RAISE NOTICE '📚 DOCUMENTACIÓN:';
    RAISE NOTICE '   • README.md - Información general';
    RAISE NOTICE '   • database/API.md - Documentación de API';
    RAISE NOTICE '';
END $$;

\echo '✅ Instalación de JARSS completada exitosamente!'
