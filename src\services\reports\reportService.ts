import { baseApi } from '../api/baseApi';
import { ApiResponse, PaginatedResponse } from '@/types/common';

export interface Report {
  id: string;
  title: string;
  description: string;
  type: 'balance' | 'resultados' | 'flujo' | 'personalizados';
  status: 'available' | 'coming_soon';
  createdAt: Date;
  updatedAt: Date;
}

export interface ReportFilters {
  type?: Report['type'];
  status?: Report['status'];
  search?: string;
}

export const reportService = {
  async getReports(filters?: ReportFilters): Promise<ApiResponse<Report[]>> {
    return baseApi.get<Report[]>('/reports', filters as Record<string, string>);
  },

  async getReportById(id: string): Promise<ApiResponse<Report>> {
    return baseApi.get<Report>(`/reports/${id}`);
  },

  async createReport(report: Omit<Report, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<Report>> {
    return baseApi.post<Report>('/reports', report);
  },

  async updateReport(id: string, report: Partial<Report>): Promise<ApiResponse<Report>> {
    return baseApi.put<Report>(`/reports/${id}`, report);
  },

  async deleteReport(id: string): Promise<ApiResponse<void>> {
    return baseApi.delete<void>(`/reports/${id}`);
  },

  async exportReport(id: string, format: 'pdf' | 'excel'): Promise<ApiResponse<Blob>> {
    return baseApi.get<Blob>(`/reports/${id}/export`, { format });
  },
}; 