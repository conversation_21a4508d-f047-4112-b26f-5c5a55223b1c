import { baseApi } from '../api/baseApi';
import { ApiResponse, PaginatedResponse } from '@/types/common';
import { Account, FinancialSummary, Transaction, TransactionFilters } from '@/types/finance';

export const financeService = {
  // Transacciones
  async getTransactions(filters?: TransactionFilters): Promise<PaginatedResponse<Transaction>> {
    return baseApi.get<Transaction[]>('/transactions', filters as Record<string, string>);
  },

  async createTransaction(transaction: Omit<Transaction, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<Transaction>> {
    return baseApi.post<Transaction>('/transactions', transaction);
  },

  async updateTransaction(id: string, transaction: Partial<Transaction>): Promise<ApiResponse<Transaction>> {
    return baseApi.put<Transaction>(`/transactions/${id}`, transaction);
  },

  async deleteTransaction(id: string): Promise<ApiResponse<void>> {
    return baseApi.delete<void>(`/transactions/${id}`);
  },

  // Cuentas
  async getAccounts(): Promise<ApiResponse<Account[]>> {
    return baseApi.get<Account[]>('/accounts');
  },

  async createAccount(account: Omit<Account, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<Account>> {
    return baseApi.post<Account>('/accounts', account);
  },

  async updateAccount(id: string, account: Partial<Account>): Promise<ApiResponse<Account>> {
    return baseApi.put<Account>(`/accounts/${id}`, account);
  },

  async deleteAccount(id: string): Promise<ApiResponse<void>> {
    return baseApi.delete<void>(`/accounts/${id}`);
  },

  // Resumen Financiero
  async getFinancialSummary(): Promise<ApiResponse<FinancialSummary>> {
    return baseApi.get<FinancialSummary>('/finance/summary');
  },
}; 