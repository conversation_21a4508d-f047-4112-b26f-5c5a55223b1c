-- JARSS - Funciones y Triggers
-- Base de datos PostgreSQL
-- Fecha: 2025-01-27

-- =====================================================
-- FUNCIONES UTILITARIAS
-- =====================================================

-- Función para actualizar updated_at automáticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Función para generar hash de contraseña
CREATE OR REPLACE FUNCTION hash_password(password TEXT)
RETURNS TEXT AS $$
BEGIN
    RETURN crypt(password, gen_salt('bf'));
END;
$$ LANGUAGE plpgsql;

-- Función para verificar contraseña
CREATE OR REPLACE FUNCTION verify_password(password TEXT, hash TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN hash = crypt(password, hash);
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FUNCIONES PARA CÁLCULOS DOMINICANOS
-- =====================================================

-- Función para calcular deducciones dominicanas
CREATE OR REPLACE FUNCTION calculate_dominican_deductions(
    gross_salary DECIMAL(12,2),
    company_uuid UUID DEFAULT NULL
)
RETURNS TABLE(
    afp_amount DECIMAL(12,2),
    sfs_amount DECIMAL(12,2),
    infotep_amount DECIMAL(12,2),
    isr_amount DECIMAL(12,2),
    total_deductions DECIMAL(12,2),
    net_salary DECIMAL(12,2)
) AS $$
DECLARE
    afp_rate DECIMAL(8,6) := 0.0287; -- 2.87%
    sfs_rate DECIMAL(8,6) := 0.0304; -- 3.04%
    infotep_rate DECIMAL(8,6) := 0.01; -- 1%
    isr_exempt_amount DECIMAL(12,2) := 416220; -- Exención anual ISR 2024
    monthly_exempt DECIMAL(12,2) := isr_exempt_amount / 12;
    taxable_income DECIMAL(12,2);
BEGIN
    -- Obtener tasas personalizadas si se proporciona company_id
    IF company_uuid IS NOT NULL THEN
        SELECT rate INTO afp_rate 
        FROM tax_configurations 
        WHERE company_id = company_uuid AND tax_type = 'afp' AND is_active = true
        ORDER BY effective_from DESC LIMIT 1;
        
        SELECT rate INTO sfs_rate 
        FROM tax_configurations 
        WHERE company_id = company_uuid AND tax_type = 'sfs' AND is_active = true
        ORDER BY effective_from DESC LIMIT 1;
        
        SELECT rate INTO infotep_rate 
        FROM tax_configurations 
        WHERE company_id = company_uuid AND tax_type = 'infotep' AND is_active = true
        ORDER BY effective_from DESC LIMIT 1;
    END IF;
    
    -- Calcular AFP
    afp_amount := ROUND(gross_salary * afp_rate, 2);
    
    -- Calcular SFS
    sfs_amount := ROUND(gross_salary * sfs_rate, 2);
    
    -- Calcular INFOTEP
    infotep_amount := ROUND(gross_salary * infotep_rate, 2);
    
    -- Calcular ISR (simplificado)
    taxable_income := gross_salary - afp_amount - sfs_amount;
    
    IF taxable_income <= monthly_exempt THEN
        isr_amount := 0;
    ELSE
        -- Aplicar tabla de ISR simplificada (15% sobre el exceso)
        isr_amount := ROUND((taxable_income - monthly_exempt) * 0.15, 2);
    END IF;
    
    -- Calcular totales
    total_deductions := afp_amount + sfs_amount + infotep_amount + isr_amount;
    net_salary := gross_salary - total_deductions;
    
    RETURN NEXT;
END;
$$ LANGUAGE plpgsql;

-- Función para calcular ITBIS
CREATE OR REPLACE FUNCTION calculate_itbis(
    base_amount DECIMAL(15,2),
    company_uuid UUID DEFAULT NULL,
    is_exempt BOOLEAN DEFAULT false
)
RETURNS DECIMAL(15,2) AS $$
DECLARE
    itbis_rate DECIMAL(8,6) := 0.18; -- 18%
BEGIN
    IF is_exempt THEN
        RETURN 0;
    END IF;
    
    -- Obtener tasa personalizada si se proporciona company_id
    IF company_uuid IS NOT NULL THEN
        SELECT rate INTO itbis_rate 
        FROM tax_configurations 
        WHERE company_id = company_uuid AND tax_type = 'itbis' AND is_active = true
        ORDER BY effective_from DESC LIMIT 1;
    END IF;
    
    RETURN ROUND(base_amount * itbis_rate, 2);
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FUNCIONES PARA GESTIÓN DE BALANCES
-- =====================================================

-- Función para actualizar balance de cuenta
CREATE OR REPLACE FUNCTION update_account_balance()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- Actualizar balance según el tipo de transacción
        IF NEW.type = 'ingreso' THEN
            UPDATE financial_accounts 
            SET balance = balance + NEW.amount,
                updated_at = NOW()
            WHERE id = NEW.account_id;
        ELSIF NEW.type = 'gasto' THEN
            UPDATE financial_accounts 
            SET balance = balance - NEW.amount,
                updated_at = NOW()
            WHERE id = NEW.account_id;
        END IF;
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        -- Si cambió el monto o la cuenta, recalcular
        IF OLD.amount != NEW.amount OR OLD.account_id != NEW.account_id OR OLD.type != NEW.type THEN
            -- Revertir transacción anterior
            IF OLD.type = 'ingreso' THEN
                UPDATE financial_accounts 
                SET balance = balance - OLD.amount,
                    updated_at = NOW()
                WHERE id = OLD.account_id;
            ELSIF OLD.type = 'gasto' THEN
                UPDATE financial_accounts 
                SET balance = balance + OLD.amount,
                    updated_at = NOW()
                WHERE id = OLD.account_id;
            END IF;
            
            -- Aplicar nueva transacción
            IF NEW.type = 'ingreso' THEN
                UPDATE financial_accounts 
                SET balance = balance + NEW.amount,
                    updated_at = NOW()
                WHERE id = NEW.account_id;
            ELSIF NEW.type = 'gasto' THEN
                UPDATE financial_accounts 
                SET balance = balance - NEW.amount,
                    updated_at = NOW()
                WHERE id = NEW.account_id;
            END IF;
        END IF;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        -- Revertir transacción
        IF OLD.type = 'ingreso' THEN
            UPDATE financial_accounts 
            SET balance = balance - OLD.amount,
                updated_at = NOW()
            WHERE id = OLD.account_id;
        ELSIF OLD.type = 'gasto' THEN
            UPDATE financial_accounts 
            SET balance = balance + OLD.amount,
                updated_at = NOW()
            WHERE id = OLD.account_id;
        END IF;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FUNCIONES PARA AUDITORÍA
-- =====================================================

-- Función para auditoría automática
CREATE OR REPLACE FUNCTION audit_trigger()
RETURNS TRIGGER AS $$
DECLARE
    company_uuid UUID;
    user_uuid UUID;
BEGIN
    -- Obtener company_id y user_id del contexto
    company_uuid := COALESCE(NEW.company_id, OLD.company_id);
    
    -- Para obtener user_id, necesitarías implementar un contexto de sesión
    -- Por ahora usaremos NULL
    user_uuid := NULL;
    
    IF TG_OP = 'INSERT' THEN
        INSERT INTO audit_logs (company_id, user_id, table_name, record_id, action, new_values)
        VALUES (company_uuid, user_uuid, TG_TABLE_NAME, NEW.id, 'INSERT', to_jsonb(NEW));
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO audit_logs (company_id, user_id, table_name, record_id, action, old_values, new_values)
        VALUES (company_uuid, user_uuid, TG_TABLE_NAME, NEW.id, 'UPDATE', to_jsonb(OLD), to_jsonb(NEW));
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        INSERT INTO audit_logs (company_id, user_id, table_name, record_id, action, old_values)
        VALUES (company_uuid, user_uuid, TG_TABLE_NAME, OLD.id, 'DELETE', to_jsonb(OLD));
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FUNCIONES PARA PRESUPUESTOS
-- =====================================================

-- Función para calcular varianza presupuestaria
CREATE OR REPLACE FUNCTION update_budget_variance()
RETURNS TRIGGER AS $$
BEGIN
    NEW.variance := NEW.actual_amount - NEW.budgeted_amount;
    
    IF NEW.budgeted_amount != 0 THEN
        NEW.variance_percentage := ROUND((NEW.variance / NEW.budgeted_amount) * 100, 2);
    ELSE
        NEW.variance_percentage := 0;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FUNCIONES PARA REPORTES
-- =====================================================

-- Función para obtener balance general
CREATE OR REPLACE FUNCTION get_balance_sheet(
    company_uuid UUID,
    as_of_date DATE DEFAULT CURRENT_DATE
)
RETURNS TABLE(
    category VARCHAR(50),
    subcategory VARCHAR(100),
    amount DECIMAL(15,2)
) AS $$
BEGIN
    -- Activos (Cuentas bancarias y por cobrar)
    RETURN QUERY
    SELECT 
        'ACTIVOS'::VARCHAR(50) as category,
        'Efectivo y Equivalentes'::VARCHAR(100) as subcategory,
        COALESCE(SUM(fa.balance), 0) as amount
    FROM financial_accounts fa
    WHERE fa.company_id = company_uuid 
    AND fa.is_active = true
    AND fa.type IN ('checking', 'savings');
    
    RETURN QUERY
    SELECT 
        'ACTIVOS'::VARCHAR(50) as category,
        'Cuentas por Cobrar'::VARCHAR(100) as subcategory,
        COALESCE(SUM(ar.balance), 0) as amount
    FROM accounts_receivable ar
    WHERE ar.company_id = company_uuid 
    AND ar.status = 'pending';
    
    -- Pasivos (Cuentas por pagar)
    RETURN QUERY
    SELECT 
        'PASIVOS'::VARCHAR(50) as category,
        'Cuentas por Pagar'::VARCHAR(100) as subcategory,
        COALESCE(SUM(ap.balance), 0) as amount
    FROM accounts_payable ap
    WHERE ap.company_id = company_uuid 
    AND ap.status = 'pending';
    
    -- Capital (calculado como diferencia)
    RETURN QUERY
    SELECT 
        'CAPITAL'::VARCHAR(50) as category,
        'Capital Contable'::VARCHAR(100) as subcategory,
        (
            COALESCE((SELECT SUM(fa.balance) FROM financial_accounts fa WHERE fa.company_id = company_uuid AND fa.is_active = true), 0) +
            COALESCE((SELECT SUM(ar.balance) FROM accounts_receivable ar WHERE ar.company_id = company_uuid AND ar.status = 'pending'), 0) -
            COALESCE((SELECT SUM(ap.balance) FROM accounts_payable ap WHERE ap.company_id = company_uuid AND ap.status = 'pending'), 0)
        ) as amount;
END;
$$ LANGUAGE plpgsql;
