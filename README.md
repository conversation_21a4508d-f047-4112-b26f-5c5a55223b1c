# JARRS Inventory

## Sistema de Inventario y Gestión Financiera

JARRS Inventory es una aplicación completa para la gestión de inventario y finanzas empresariales. Diseñada para ofrecer una experiencia de usuario intuitiva y eficiente, permite a las empresas gestionar su inventario, finanzas, reportes y más desde una única plataforma.

## Características principales

- **Panel de control**: Visualización rápida de métricas clave y estado financiero
- **Gestión financiera**: Control de ingresos, gastos e inversiones
- **Reportes detallados**: Análisis de datos financieros y de inventario
- **Cumplimiento fiscal**: Seguimiento de obligaciones fiscales
- **Control presupuestario**: Planificación y seguimiento de presupuestos
- **Gestión de nómina**: Administración de pagos a empleados
- **Bancos y proveedores**: Gestión de cuentas bancarias y relaciones con proveedores
- **Configuración personalizada**: Adaptación del sistema a las necesidades específicas

## Tecnologías utilizadas

Este proyecto está construido con:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS
- Supabase

## Instalación y ejecución local

Para ejecutar el proyecto localmente, sigue estos pasos:

```sh
# Paso 1: Clonar el repositorio
git clone <URL_DEL_REPOSITORIO>

# Paso 2: Navegar al directorio del proyecto
cd jarrs-inventory

# Paso 3: Instalar las dependencias necesarias
npm install

# Paso 4: Iniciar el servidor de desarrollo
npm run dev
```

## Despliegue

Para desplegar la aplicación en un entorno de producción:

```sh
# Construir la aplicación para producción
npm run build

# Vista previa de la versión de producción localmente
npm run preview
```

## Licencia

JARRS Inventory © 2025. Todos los derechos reservados.
