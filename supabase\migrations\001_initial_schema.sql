-- JARSS - Sistema Integral de Gestión Financiera
-- Migración inicial: Esquemas base y tablas principales
-- Fecha: 2025-01-27

-- Habilitar extensiones necesarias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =====================================================
-- ESQUEMAS Y TIPOS ENUMERADOS
-- =====================================================

-- Tipos para transacciones financieras
CREATE TYPE transaction_type AS ENUM ('ingreso', 'gasto', 'transferencia');
CREATE TYPE transaction_status AS ENUM ('pending', 'completed', 'cancelled');
CREATE TYPE account_type AS ENUM ('checking', 'savings', 'credit', 'investment');

-- Tipos para empleados y nómina
CREATE TYPE employee_status AS ENUM ('active', 'inactive', 'on_leave');
CREATE TYPE payroll_status AS ENUM ('pending', 'processing', 'completed', 'paid');

-- Tipos para impuestos
CREATE TYPE tax_type AS ENUM ('itbis', 'isr', 'afp', 'sfs', 'infotep', 'other');
CREATE TYPE tax_status AS ENUM ('pending', 'paid', 'overdue', 'cancelled');

-- Tipos para reportes
CREATE TYPE report_type AS ENUM ('balance', 'income_statement', 'cash_flow', 'custom');
CREATE TYPE report_status AS ENUM ('draft', 'final', 'submitted');

-- =====================================================
-- TABLA: USUARIOS Y PERFILES
-- =====================================================

-- Perfiles de usuario (extiende auth.users)
CREATE TABLE profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT,
    company_name TEXT,
    phone TEXT,
    address TEXT,
    tax_id TEXT, -- RNC en República Dominicana
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- MÓDULO: GESTIÓN FINANCIERA Y CONTABLE
-- =====================================================

-- Cuentas financieras
CREATE TABLE financial_accounts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    type account_type NOT NULL,
    balance DECIMAL(15,2) DEFAULT 0.00,
    currency TEXT DEFAULT 'DOP',
    bank_name TEXT,
    account_number TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Categorías de transacciones
CREATE TABLE transaction_categories (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    type transaction_type NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Transacciones financieras
CREATE TABLE financial_transactions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    account_id UUID REFERENCES financial_accounts(id) ON DELETE SET NULL,
    category_id UUID REFERENCES transaction_categories(id) ON DELETE SET NULL,
    type transaction_type NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    description TEXT NOT NULL,
    reference_number TEXT,
    transaction_date DATE NOT NULL,
    status transaction_status DEFAULT 'pending',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- MÓDULO: CUMPLIMIENTO FISCAL (REPÚBLICA DOMINICANA)
-- =====================================================

-- Configuración de impuestos dominicanos
CREATE TABLE tax_configurations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    tax_type tax_type NOT NULL,
    rate DECIMAL(5,4) NOT NULL, -- Ej: 0.1800 para ITBIS 18%
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    effective_from DATE NOT NULL,
    effective_to DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Obligaciones fiscales
CREATE TABLE tax_obligations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    tax_type tax_type NOT NULL,
    due_date DATE NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    status tax_status DEFAULT 'pending',
    payment_date DATE,
    reference_number TEXT,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Declaraciones fiscales (formularios DGII)
CREATE TABLE tax_declarations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    form_type TEXT NOT NULL, -- 606, 607, etc.
    period_year INTEGER NOT NULL,
    period_month INTEGER, -- NULL para declaraciones anuales
    total_amount DECIMAL(15,2) DEFAULT 0.00,
    status report_status DEFAULT 'draft',
    submission_date DATE,
    file_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
