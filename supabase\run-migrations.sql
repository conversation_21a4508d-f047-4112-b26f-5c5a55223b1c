-- JARSS - Sistema Integral de Gestión Financiera
-- Script para ejecutar todas las migraciones
-- Ejecutar este archivo en el SQL Editor de Supabase

-- =====================================================
-- EJECUTAR MIGRACIONES EN ORDEN
-- =====================================================

-- Migración 001: Esquema inicial
\i 001_initial_schema.sql

-- Migración 002: Nómina y Presupuestos
\i 002_nomina_presupuestos.sql

-- Migración 003: Reportes e Inversiones
\i 003_reportes_inversiones.sql

-- Migración 004: Políticas RLS
\i 004_rls_policies.sql

-- Migración 005: Funciones y Triggers
\i 005_functions_triggers.sql

-- Migración 006: Datos iniciales
\i 006_seed_data.sql

-- =====================================================
-- VERIFICACIÓN DE MIGRACIONES
-- =====================================================

-- Verificar que todas las tablas se crearon correctamente
SELECT 
    schemaname,
    tablename,
    tableowner
FROM pg_tables 
WHERE schemaname = 'public' 
ORDER BY tablename;

-- Verificar que RLS está habilitado
SELECT 
    schemaname,
    tablename,
    rowsecurity
FROM pg_tables 
WHERE schemaname = 'public' 
    AND rowsecurity = true
ORDER BY tablename;

-- Verificar funciones creadas
SELECT 
    routine_name,
    routine_type
FROM information_schema.routines 
WHERE routine_schema = 'public'
    AND routine_name LIKE '%jarss%' OR routine_name LIKE '%dominican%'
ORDER BY routine_name;

-- Verificar triggers creados
SELECT 
    trigger_name,
    event_object_table,
    action_timing,
    event_manipulation
FROM information_schema.triggers 
WHERE trigger_schema = 'public'
ORDER BY event_object_table, trigger_name;

-- =====================================================
-- MENSAJE DE CONFIRMACIÓN
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '✅ JARSS Database Setup Complete!';
    RAISE NOTICE '📊 All tables, functions, triggers, and RLS policies have been created.';
    RAISE NOTICE '🔐 Row Level Security is enabled for all tables.';
    RAISE NOTICE '🇩🇴 Dominican Republic tax configurations are ready.';
    RAISE NOTICE '💼 Default categories and configurations will be created for new users.';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 Your JARSS system is ready to use!';
END $$;
