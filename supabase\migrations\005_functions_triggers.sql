-- JARSS - Sistema Integral de Gestión Financiera
-- Migración 005: Funciones y Triggers
-- Fecha: 2025-01-27

-- =====================================================
-- FUNCIONES UTILITARIAS
-- =====================================================

-- Función para actualizar updated_at automáticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Función para calcular deducciones dominicanas
CREATE OR REPLACE FUNCTION calculate_dominican_deductions(
    gross_salary DECIMAL(12,2)
)
RETURNS TABLE(
    afp_amount DECIMAL(12,2),
    sfs_amount DECIMAL(12,2),
    infotep_amount DECIMAL(12,2),
    isr_amount DECIMAL(12,2)
) AS $$
DECLARE
    afp_rate DECIMAL(5,4) := 0.0287; -- 2.87%
    sfs_rate DECIMAL(5,4) := 0.0304; -- 3.04%
    infotep_rate DECIMAL(5,4) := 0.01; -- 1%
    isr_exempt_amount DECIMAL(12,2) := 416220; -- Exención anual ISR 2024
    monthly_exempt DECIMAL(12,2) := isr_exempt_amount / 12;
    taxable_income DECIMAL(12,2);
BEGIN
    -- Calcular AFP
    afp_amount := ROUND(gross_salary * afp_rate, 2);
    
    -- Calcular SFS
    sfs_amount := ROUND(gross_salary * sfs_rate, 2);
    
    -- Calcular INFOTEP
    infotep_amount := ROUND(gross_salary * infotep_rate, 2);
    
    -- Calcular ISR (simplificado)
    taxable_income := gross_salary - afp_amount - sfs_amount;
    
    IF taxable_income <= monthly_exempt THEN
        isr_amount := 0;
    ELSE
        -- Aplicar tabla de ISR simplificada (15% sobre el exceso)
        isr_amount := ROUND((taxable_income - monthly_exempt) * 0.15, 2);
    END IF;
    
    RETURN NEXT;
END;
$$ LANGUAGE plpgsql;

-- Función para calcular ITBIS
CREATE OR REPLACE FUNCTION calculate_itbis(
    base_amount DECIMAL(15,2),
    is_exempt BOOLEAN DEFAULT false
)
RETURNS DECIMAL(15,2) AS $$
DECLARE
    itbis_rate DECIMAL(5,4) := 0.18; -- 18%
BEGIN
    IF is_exempt THEN
        RETURN 0;
    ELSE
        RETURN ROUND(base_amount * itbis_rate, 2);
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Función para actualizar balance de cuenta
CREATE OR REPLACE FUNCTION update_account_balance()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- Actualizar balance según el tipo de transacción
        IF NEW.type = 'ingreso' THEN
            UPDATE financial_accounts 
            SET balance = balance + NEW.amount,
                updated_at = NOW()
            WHERE id = NEW.account_id;
        ELSIF NEW.type = 'gasto' THEN
            UPDATE financial_accounts 
            SET balance = balance - NEW.amount,
                updated_at = NOW()
            WHERE id = NEW.account_id;
        END IF;
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        -- Si cambió el monto o la cuenta, recalcular
        IF OLD.amount != NEW.amount OR OLD.account_id != NEW.account_id OR OLD.type != NEW.type THEN
            -- Revertir transacción anterior
            IF OLD.type = 'ingreso' THEN
                UPDATE financial_accounts 
                SET balance = balance - OLD.amount,
                    updated_at = NOW()
                WHERE id = OLD.account_id;
            ELSIF OLD.type = 'gasto' THEN
                UPDATE financial_accounts 
                SET balance = balance + OLD.amount,
                    updated_at = NOW()
                WHERE id = OLD.account_id;
            END IF;
            
            -- Aplicar nueva transacción
            IF NEW.type = 'ingreso' THEN
                UPDATE financial_accounts 
                SET balance = balance + NEW.amount,
                    updated_at = NOW()
                WHERE id = NEW.account_id;
            ELSIF NEW.type = 'gasto' THEN
                UPDATE financial_accounts 
                SET balance = balance - NEW.amount,
                    updated_at = NOW()
                WHERE id = NEW.account_id;
            END IF;
        END IF;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        -- Revertir transacción
        IF OLD.type = 'ingreso' THEN
            UPDATE financial_accounts 
            SET balance = balance - OLD.amount,
                updated_at = NOW()
            WHERE id = OLD.account_id;
        ELSIF OLD.type = 'gasto' THEN
            UPDATE financial_accounts 
            SET balance = balance + OLD.amount,
                updated_at = NOW()
            WHERE id = OLD.account_id;
        END IF;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Función para auditoría automática
CREATE OR REPLACE FUNCTION audit_trigger()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        INSERT INTO audit_logs (user_id, table_name, record_id, action, new_values)
        VALUES (auth.uid(), TG_TABLE_NAME, NEW.id, 'INSERT', to_jsonb(NEW));
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO audit_logs (user_id, table_name, record_id, action, old_values, new_values)
        VALUES (auth.uid(), TG_TABLE_NAME, NEW.id, 'UPDATE', to_jsonb(OLD), to_jsonb(NEW));
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        INSERT INTO audit_logs (user_id, table_name, record_id, action, old_values)
        VALUES (auth.uid(), TG_TABLE_NAME, OLD.id, 'DELETE', to_jsonb(OLD));
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Función para calcular varianza presupuestaria
CREATE OR REPLACE FUNCTION update_budget_variance()
RETURNS TRIGGER AS $$
BEGIN
    NEW.variance := NEW.actual_amount - NEW.budgeted_amount;
    
    IF NEW.budgeted_amount != 0 THEN
        NEW.variance_percentage := ROUND((NEW.variance / NEW.budgeted_amount) * 100, 2);
    ELSE
        NEW.variance_percentage := 0;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- TRIGGERS
-- =====================================================

-- Triggers para updated_at
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_financial_accounts_updated_at BEFORE UPDATE ON financial_accounts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_financial_transactions_updated_at BEFORE UPDATE ON financial_transactions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tax_obligations_updated_at BEFORE UPDATE ON tax_obligations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tax_declarations_updated_at BEFORE UPDATE ON tax_declarations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_employees_updated_at BEFORE UPDATE ON employees
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_payroll_periods_updated_at BEFORE UPDATE ON payroll_periods
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_payroll_entries_updated_at BEFORE UPDATE ON payroll_entries
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_budgets_updated_at BEFORE UPDATE ON budgets
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_budget_lines_updated_at BEFORE UPDATE ON budget_lines
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_suppliers_updated_at BEFORE UPDATE ON suppliers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_customers_updated_at BEFORE UPDATE ON customers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_accounts_payable_updated_at BEFORE UPDATE ON accounts_payable
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_accounts_receivable_updated_at BEFORE UPDATE ON accounts_receivable
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_financial_reports_updated_at BEFORE UPDATE ON financial_reports
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_report_templates_updated_at BEFORE UPDATE ON report_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_investments_updated_at BEFORE UPDATE ON investments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_configurations_updated_at BEFORE UPDATE ON system_configurations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bank_statements_updated_at BEFORE UPDATE ON bank_statements
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_recurring_payments_updated_at BEFORE UPDATE ON recurring_payments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Trigger para actualizar balance de cuentas
CREATE TRIGGER update_account_balance_trigger
    AFTER INSERT OR UPDATE OR DELETE ON financial_transactions
    FOR EACH ROW EXECUTE FUNCTION update_account_balance();

-- Trigger para calcular varianza presupuestaria
CREATE TRIGGER calculate_budget_variance_trigger
    BEFORE INSERT OR UPDATE ON budget_lines
    FOR EACH ROW EXECUTE FUNCTION update_budget_variance();

-- Triggers de auditoría para tablas críticas
CREATE TRIGGER audit_financial_transactions_trigger
    AFTER INSERT OR UPDATE OR DELETE ON financial_transactions
    FOR EACH ROW EXECUTE FUNCTION audit_trigger();

CREATE TRIGGER audit_payroll_entries_trigger
    AFTER INSERT OR UPDATE OR DELETE ON payroll_entries
    FOR EACH ROW EXECUTE FUNCTION audit_trigger();

CREATE TRIGGER audit_tax_obligations_trigger
    AFTER INSERT OR UPDATE OR DELETE ON tax_obligations
    FOR EACH ROW EXECUTE FUNCTION audit_trigger();
