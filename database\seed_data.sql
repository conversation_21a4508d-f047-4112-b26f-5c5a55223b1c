-- JARSS - Datos Iniciales
-- Base de datos PostgreSQL
-- Fecha: 2025-01-27

-- =====================================================
-- FUNCIONES PARA CREAR DATOS INICIALES
-- =====================================================

-- Función para crear categorías por defecto para una empresa
CREATE OR REPLACE FUNCTION create_default_categories(company_uuid UUID)
RETURNS VOID AS $$
BEGIN
    -- Categorías de Ingresos
    INSERT INTO transaction_categories (company_id, name, type, description) VALUES
    (company_uuid, 'Ventas', 'ingreso', 'Ingresos por ventas de productos o servicios'),
    (company_uuid, 'Servicios', 'ingreso', 'Ingresos por prestación de servicios'),
    (company_uuid, 'Comisiones', 'ingreso', 'Ingresos por comisiones'),
    (company_uuid, 'Intereses', 'ingreso', 'Ingresos por intereses bancarios'),
    (company_uuid, 'Dividendos', 'ingreso', 'Ingresos por dividendos de inversiones'),
    (company_uuid, 'Otros Ingresos', 'ingreso', 'Otros tipos de ingresos');

    -- Categorías de Gastos
    INSERT INTO transaction_categories (company_id, name, type, description) VALUES
    (company_uuid, 'Compras', 'gasto', 'Compras de inventario o materias primas'),
    (company_uuid, 'Nómina', 'gasto', 'Gastos de nómina y beneficios'),
    (company_uuid, 'Servicios Públicos', 'gasto', 'Electricidad, agua, teléfono, internet'),
    (company_uuid, 'Alquiler', 'gasto', 'Alquiler de oficinas o locales'),
    (company_uuid, 'Marketing', 'gasto', 'Gastos de publicidad y marketing'),
    (company_uuid, 'Transporte', 'gasto', 'Gastos de transporte y combustible'),
    (company_uuid, 'Suministros de Oficina', 'gasto', 'Papelería y suministros'),
    (company_uuid, 'Mantenimiento', 'gasto', 'Mantenimiento de equipos e instalaciones'),
    (company_uuid, 'Seguros', 'gasto', 'Pólizas de seguros'),
    (company_uuid, 'Impuestos', 'gasto', 'Pagos de impuestos'),
    (company_uuid, 'Servicios Profesionales', 'gasto', 'Honorarios profesionales'),
    (company_uuid, 'Otros Gastos', 'gasto', 'Otros tipos de gastos');
END;
$$ LANGUAGE plpgsql;

-- Función para crear configuraciones fiscales dominicanas
CREATE OR REPLACE FUNCTION create_default_tax_config(company_uuid UUID)
RETURNS VOID AS $$
BEGIN
    INSERT INTO tax_configurations (company_id, tax_type, rate, description, effective_from) VALUES
    (company_uuid, 'itbis', 0.180000, 'Impuesto sobre Transferencias de Bienes Industrializados y Servicios', '2024-01-01'),
    (company_uuid, 'afp', 0.028700, 'Aporte Fondo de Pensiones', '2024-01-01'),
    (company_uuid, 'sfs', 0.030400, 'Seguro Familiar de Salud', '2024-01-01'),
    (company_uuid, 'infotep', 0.010000, 'Instituto Nacional de Formación Técnico Profesional', '2024-01-01');
END;
$$ LANGUAGE plpgsql;

-- Función para crear categorías presupuestarias por defecto
CREATE OR REPLACE FUNCTION create_default_budget_categories(company_uuid UUID)
RETURNS VOID AS $$
BEGIN
    -- Categorías de Ingresos Presupuestarios
    INSERT INTO budget_categories (company_id, name, type, description) VALUES
    (company_uuid, 'Ventas Proyectadas', 'ingreso', 'Proyección de ventas mensuales'),
    (company_uuid, 'Servicios Proyectados', 'ingreso', 'Proyección de ingresos por servicios'),
    (company_uuid, 'Ingresos Financieros', 'ingreso', 'Intereses y dividendos esperados'),
    (company_uuid, 'Otros Ingresos Proyectados', 'ingreso', 'Otros ingresos esperados');

    -- Categorías de Gastos Presupuestarios
    INSERT INTO budget_categories (company_id, name, type, description) VALUES
    (company_uuid, 'Costo de Ventas', 'gasto', 'Costos directos de productos vendidos'),
    (company_uuid, 'Gastos de Personal', 'gasto', 'Nómina y beneficios del personal'),
    (company_uuid, 'Gastos Operativos', 'gasto', 'Gastos operativos generales'),
    (company_uuid, 'Gastos de Marketing', 'gasto', 'Presupuesto para marketing y publicidad'),
    (company_uuid, 'Gastos Administrativos', 'gasto', 'Gastos administrativos y de oficina'),
    (company_uuid, 'Gastos Financieros', 'gasto', 'Intereses y comisiones bancarias');
END;
$$ LANGUAGE plpgsql;

-- Función para crear tipos de inversión por defecto
CREATE OR REPLACE FUNCTION create_default_investment_types(company_uuid UUID)
RETURNS VOID AS $$
BEGIN
    INSERT INTO investment_types (company_id, name, description, risk_level) VALUES
    (company_uuid, 'Certificados de Depósito', 'Certificados de depósito bancarios', 'low'),
    (company_uuid, 'Bonos del Estado', 'Bonos emitidos por el gobierno dominicano', 'low'),
    (company_uuid, 'Acciones Locales', 'Acciones de empresas dominicanas', 'high'),
    (company_uuid, 'Fondos Mutuos', 'Fondos de inversión diversificados', 'medium'),
    (company_uuid, 'Bienes Raíces', 'Inversiones en propiedades', 'medium'),
    (company_uuid, 'Divisas', 'Inversiones en moneda extranjera', 'medium'),
    (company_uuid, 'Otros', 'Otros tipos de inversiones', 'medium');
END;
$$ LANGUAGE plpgsql;

-- Función para crear configuraciones del sistema por defecto
CREATE OR REPLACE FUNCTION create_default_system_config(company_uuid UUID)
RETURNS VOID AS $$
BEGIN
    INSERT INTO system_configurations (company_id, key, value, description, category) VALUES
    (company_uuid, 'default_currency', 'DOP', 'Moneda por defecto del sistema', 'general'),
    (company_uuid, 'date_format', 'DD/MM/YYYY', 'Formato de fecha por defecto', 'general'),
    (company_uuid, 'decimal_places', '2', 'Número de decimales para montos', 'general'),
    (company_uuid, 'fiscal_year_start', '01/01', 'Inicio del año fiscal (DD/MM)', 'fiscal'),
    (company_uuid, 'tax_period', 'monthly', 'Período de declaraciones fiscales', 'fiscal'),
    (company_uuid, 'payroll_frequency', 'monthly', 'Frecuencia de nómina', 'payroll'),
    (company_uuid, 'backup_frequency', 'daily', 'Frecuencia de respaldos automáticos', 'system'),
    (company_uuid, 'notification_email', 'true', 'Enviar notificaciones por email', 'notifications'),
    (company_uuid, 'auto_reconcile', 'false', 'Conciliación bancaria automática', 'banking'),
    (company_uuid, 'budget_alerts', 'true', 'Alertas de variación presupuestaria', 'budget'),
    (company_uuid, 'tax_alerts', 'true', 'Alertas de vencimientos fiscales', 'tax');
END;
$$ LANGUAGE plpgsql;

-- Función para crear departamentos básicos
CREATE OR REPLACE FUNCTION create_default_departments(company_uuid UUID)
RETURNS VOID AS $$
BEGIN
    INSERT INTO departments (company_id, name, description) VALUES
    (company_uuid, 'Administración', 'Departamento administrativo y gerencial'),
    (company_uuid, 'Finanzas', 'Departamento de finanzas y contabilidad'),
    (company_uuid, 'Ventas', 'Departamento de ventas y marketing'),
    (company_uuid, 'Operaciones', 'Departamento de operaciones y producción'),
    (company_uuid, 'Recursos Humanos', 'Departamento de recursos humanos'),
    (company_uuid, 'Tecnología', 'Departamento de tecnología e informática');
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FUNCIÓN PRINCIPAL PARA SETUP INICIAL
-- =====================================================

-- Función que se ejecuta cuando se crea una nueva empresa
CREATE OR REPLACE FUNCTION setup_new_company(company_uuid UUID)
RETURNS VOID AS $$
BEGIN
    -- Crear categorías por defecto
    PERFORM create_default_categories(company_uuid);
    
    -- Crear configuraciones fiscales
    PERFORM create_default_tax_config(company_uuid);
    
    -- Crear categorías presupuestarias
    PERFORM create_default_budget_categories(company_uuid);
    
    -- Crear tipos de inversión
    PERFORM create_default_investment_types(company_uuid);
    
    -- Crear configuraciones del sistema
    PERFORM create_default_system_config(company_uuid);
    
    -- Crear departamentos básicos
    PERFORM create_default_departments(company_uuid);
    
    -- Crear notificación de bienvenida
    INSERT INTO notifications (company_id, title, message, type) VALUES
    (company_uuid, '¡Bienvenido a JARSS!', 'Su sistema de gestión financiera ha sido configurado exitosamente. Puede comenzar a registrar transacciones y gestionar sus finanzas.', 'success');
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- DATOS DE EJEMPLO (OPCIONAL)
-- =====================================================

-- Función para crear datos de ejemplo (solo para desarrollo/demo)
CREATE OR REPLACE FUNCTION create_demo_data(company_uuid UUID)
RETURNS VOID AS $$
DECLARE
    account_id UUID;
    budget_id UUID;
    category_id UUID;
    department_id UUID;
    employee_id UUID;
    supplier_id UUID;
    customer_id UUID;
BEGIN
    -- Crear cuenta de ejemplo
    INSERT INTO financial_accounts (company_id, name, type, balance, bank_name, account_number)
    VALUES (company_uuid, 'Cuenta Corriente Principal', 'checking', 150000.00, 'Banco Popular Dominicano', '****1234')
    RETURNING id INTO account_id;
    
    INSERT INTO financial_accounts (company_id, name, type, balance, bank_name, account_number)
    VALUES (company_uuid, 'Cuenta de Ahorros', 'savings', 75000.00, 'Banco BHD León', '****5678');

    -- Crear presupuesto de ejemplo para el año actual
    INSERT INTO budgets (company_id, name, year, description, total_income_budget, total_expense_budget)
    VALUES (company_uuid, 'Presupuesto ' || EXTRACT(YEAR FROM CURRENT_DATE), EXTRACT(YEAR FROM CURRENT_DATE), 'Presupuesto anual principal', 1200000.00, 900000.00)
    RETURNING id INTO budget_id;

    -- Crear algunas transacciones de ejemplo
    SELECT id INTO category_id FROM transaction_categories WHERE company_id = company_uuid AND name = 'Ventas' LIMIT 1;
    
    INSERT INTO financial_transactions (company_id, account_id, category_id, type, amount, description, transaction_date)
    VALUES 
    (company_uuid, account_id, category_id, 'ingreso', 25000.00, 'Venta de servicios - Cliente ABC Corp', CURRENT_DATE - INTERVAL '10 days'),
    (company_uuid, account_id, category_id, 'ingreso', 18500.00, 'Venta de productos - Cliente XYZ Ltd', CURRENT_DATE - INTERVAL '8 days'),
    (company_uuid, account_id, category_id, 'ingreso', 32000.00, 'Servicios de consultoría - Cliente DEF SA', CURRENT_DATE - INTERVAL '5 days');

    SELECT id INTO category_id FROM transaction_categories WHERE company_id = company_uuid AND name = 'Servicios Públicos' LIMIT 1;
    
    INSERT INTO financial_transactions (company_id, account_id, category_id, type, amount, description, transaction_date)
    VALUES 
    (company_uuid, account_id, category_id, 'gasto', 4500.00, 'Pago de electricidad - EDEESTE', CURRENT_DATE - INTERVAL '7 days'),
    (company_uuid, account_id, category_id, 'gasto', 2800.00, 'Pago de internet - Claro', CURRENT_DATE - INTERVAL '6 days'),
    (company_uuid, account_id, category_id, 'gasto', 1200.00, 'Pago de teléfono - Altice', CURRENT_DATE - INTERVAL '4 days');

    -- Crear empleado de ejemplo
    SELECT id INTO department_id FROM departments WHERE company_id = company_uuid AND name = 'Administración' LIMIT 1;
    
    INSERT INTO employees (company_id, department_id, employee_code, first_name, last_name, position, hire_date, base_salary, email, phone)
    VALUES (company_uuid, department_id, 'EMP001', 'Juan', 'Pérez', 'Gerente General', '2024-01-15', 85000.00, '<EMAIL>', '************')
    RETURNING id INTO employee_id;
    
    INSERT INTO employees (company_id, department_id, employee_code, first_name, last_name, position, hire_date, base_salary, email, phone)
    VALUES (company_uuid, department_id, 'EMP002', 'María', 'González', 'Contadora', '2024-02-01', 65000.00, '<EMAIL>', '************');

    -- Crear proveedor de ejemplo
    INSERT INTO suppliers (company_id, name, contact_name, email, phone, tax_id, payment_terms)
    VALUES (company_uuid, 'Suministros Oficina RD', 'Carlos Martínez', '<EMAIL>', '************', '101-12345-6', 30)
    RETURNING id INTO supplier_id;
    
    INSERT INTO suppliers (company_id, name, contact_name, email, phone, tax_id, payment_terms)
    VALUES (company_uuid, 'Tecnología Avanzada SRL', 'Ana Rodríguez', '<EMAIL>', '************', '101-54321-9', 15);

    -- Crear cliente de ejemplo
    INSERT INTO customers (company_id, name, contact_name, email, phone, tax_id, credit_limit)
    VALUES (company_uuid, 'Empresa ABC Corp', 'Luis Fernández', '<EMAIL>', '************', '101-98765-4', 100000.00)
    RETURNING id INTO customer_id;
    
    INSERT INTO customers (company_id, name, contact_name, email, phone, tax_id, credit_limit)
    VALUES (company_uuid, 'Comercial XYZ Ltd', 'Carmen Jiménez', '<EMAIL>', '************', '101-11111-1', 75000.00);

    -- Crear algunas obligaciones fiscales de ejemplo
    INSERT INTO tax_obligations (company_id, name, tax_type, due_date, amount, status)
    VALUES 
    (company_uuid, 'ITBIS Enero 2025', 'itbis', CURRENT_DATE + INTERVAL '15 days', 12500.00, 'pending'),
    (company_uuid, 'ISR Trimestral Q1 2025', 'isr', CURRENT_DATE + INTERVAL '30 days', 8750.00, 'pending');

    -- Crear cuenta por pagar de ejemplo
    INSERT INTO accounts_payable (company_id, supplier_id, invoice_number, invoice_date, due_date, amount, balance, description)
    VALUES (company_uuid, supplier_id, 'FACT-001', CURRENT_DATE - INTERVAL '5 days', CURRENT_DATE + INTERVAL '25 days', 15000.00, 15000.00, 'Compra de suministros de oficina');

    -- Crear cuenta por cobrar de ejemplo
    INSERT INTO accounts_receivable (company_id, customer_id, invoice_number, invoice_date, due_date, amount, balance, description)
    VALUES (company_uuid, customer_id, 'INV-001', CURRENT_DATE - INTERVAL '3 days', CURRENT_DATE + INTERVAL '27 days', 32000.00, 32000.00, 'Servicios de consultoría empresarial');

END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- COMENTARIOS EN LAS TABLAS PRINCIPALES
-- =====================================================

COMMENT ON TABLE users IS 'Usuarios del sistema JARSS';
COMMENT ON TABLE companies IS 'Empresas registradas en el sistema';
COMMENT ON TABLE financial_accounts IS 'Cuentas financieras (bancarias, de inversión, etc.)';
COMMENT ON TABLE financial_transactions IS 'Transacciones financieras (ingresos, gastos, transferencias)';
COMMENT ON TABLE tax_obligations IS 'Obligaciones fiscales y tributarias dominicanas';
COMMENT ON TABLE employees IS 'Registro de empleados de la empresa';
COMMENT ON TABLE payroll_entries IS 'Entradas de nómina por empleado y período';
COMMENT ON TABLE budgets IS 'Presupuestos anuales de la empresa';
COMMENT ON TABLE suppliers IS 'Proveedores de la empresa';
COMMENT ON TABLE customers IS 'Clientes de la empresa';
COMMENT ON TABLE investments IS 'Inversiones de la empresa';
COMMENT ON TABLE financial_reports IS 'Reportes financieros generados';
COMMENT ON TABLE audit_logs IS 'Registro de auditoría de cambios en el sistema';
