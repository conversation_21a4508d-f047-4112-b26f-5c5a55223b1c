# JARSS - Esquema de Base de Datos

## Descripción General

Este documento describe el esquema completo de la base de datos para JARSS (Sistema Integral de Gestión Financiera), diseñado específicamente para empresas en República Dominicana.

## Módulos del Sistema

### 1. 👤 Gestión de Usuarios
- **profiles**: Perfiles de usuario extendidos
- **system_configurations**: Configuraciones personalizadas por usuario
- **notifications**: Sistema de notificaciones
- **audit_logs**: Registro de auditoría

### 2. 💰 Gestión Financiera y Contable
- **financial_accounts**: Cuentas bancarias y financieras
- **transaction_categories**: Categorías de transacciones
- **financial_transactions**: Transacciones (ingresos, gastos, transferencias)
- **bank_statements**: Extractos bancarios
- **bank_movements**: Movimientos bancarios para conciliación

### 3. 🧾 Cumplimiento Fiscal (República Dominicana)
- **tax_configurations**: Configuración de impuestos (ITBIS 18%, AFP 2.87%, etc.)
- **tax_obligations**: Obligaciones fiscales
- **tax_declarations**: Declaraciones DGII (formularios 606, 607, etc.)

### 4. 💼 Gestión de Nómina
- **departments**: Departamentos de la empresa
- **employees**: Registro de empleados
- **payroll_periods**: Períodos de nómina
- **payroll_entries**: Entradas de nómina con deducciones dominicanas

### 5. 📊 Control Presupuestario
- **budgets**: Presupuestos anuales
- **budget_categories**: Categorías presupuestarias
- **budget_lines**: Líneas presupuestarias detalladas por mes

### 6. 🏦 Bancos y Proveedores
- **suppliers**: Proveedores
- **customers**: Clientes
- **accounts_payable**: Cuentas por pagar
- **accounts_receivable**: Cuentas por cobrar
- **recurring_payments**: Pagos programados
- **scheduled_payment_history**: Historial de pagos programados

### 7. 📈 Inversiones
- **investment_types**: Tipos de inversión
- **investments**: Inversiones
- **investment_movements**: Movimientos de inversiones

### 8. 📋 Reportes
- **financial_reports**: Reportes financieros generados
- **report_templates**: Plantillas de reportes personalizados

## Características Especiales

### 🔐 Seguridad (Row Level Security)
- Todas las tablas tienen RLS habilitado
- Los usuarios solo pueden acceder a sus propios datos
- Políticas de seguridad específicas por tabla

### 🇩🇴 Configuración Dominicana
- **ITBIS**: 18% (configurable)
- **AFP**: 2.87% (configurable)
- **SFS**: 3.04% (configurable)
- **INFOTEP**: 1% (configurable)
- **ISR**: Cálculo automático con exención anual

### ⚡ Funciones Automáticas
- **Actualización de balances**: Los balances de cuentas se actualizan automáticamente
- **Cálculo de deducciones**: Deducciones dominicanas calculadas automáticamente
- **Varianza presupuestaria**: Cálculo automático de varianzas
- **Auditoría**: Registro automático de cambios en tablas críticas

### 🔄 Triggers Implementados
- `update_updated_at`: Actualiza automáticamente el campo updated_at
- `update_account_balance`: Actualiza balances de cuentas automáticamente
- `calculate_budget_variance`: Calcula varianzas presupuestarias
- `audit_trigger`: Registra cambios para auditoría
- `setup_new_user`: Configura datos iniciales para nuevos usuarios

## Datos Iniciales

Cuando un usuario se registra, automáticamente se crean:

### Categorías de Transacciones
**Ingresos:**
- Ventas
- Servicios
- Comisiones
- Intereses
- Otros Ingresos

**Gastos:**
- Compras
- Nómina
- Servicios Públicos
- Alquiler
- Marketing
- Transporte
- Suministros de Oficina
- Mantenimiento
- Seguros
- Impuestos
- Otros Gastos

### Configuraciones Fiscales
- ITBIS: 18%
- AFP: 2.87%
- SFS: 3.04%
- INFOTEP: 1%

### Tipos de Inversión
- Certificados de Depósito (Riesgo Bajo)
- Bonos del Estado (Riesgo Bajo)
- Acciones (Riesgo Alto)
- Fondos Mutuos (Riesgo Medio)
- Bienes Raíces (Riesgo Medio)
- Otros (Riesgo Medio)

### Configuraciones del Sistema
- Moneda por defecto: DOP
- Formato de fecha: DD/MM/YYYY
- Inicio año fiscal: 01/01
- Configuraciones de empresa (vacías)
- Configuraciones de notificaciones

## Índices de Rendimiento

Se han creado índices optimizados para:
- Consultas por usuario y fecha
- Búsquedas por cuenta y categoría
- Consultas de nómina por período y empleado
- Búsquedas de obligaciones fiscales
- Consultas presupuestarias
- Auditoría y notificaciones

## Instalación

1. Ejecutar las migraciones en orden:
   ```sql
   -- En Supabase SQL Editor
   \i supabase/migrations/001_initial_schema.sql
   \i supabase/migrations/002_nomina_presupuestos.sql
   \i supabase/migrations/003_reportes_inversiones.sql
   \i supabase/migrations/004_rls_policies.sql
   \i supabase/migrations/005_functions_triggers.sql
   \i supabase/migrations/006_seed_data.sql
   ```

2. O ejecutar el script completo:
   ```sql
   \i supabase/run-migrations.sql
   ```

## Mantenimiento

### Respaldos Recomendados
- Respaldo diario automático de transacciones
- Respaldo semanal completo
- Respaldo mensual para archivo histórico

### Monitoreo
- Verificar integridad de balances semanalmente
- Revisar logs de auditoría mensualmente
- Monitorear rendimiento de consultas

## Próximas Funcionalidades

- Integración con APIs bancarias dominicanas
- Generación automática de formularios DGII
- Análisis predictivo con IA
- Sincronización con sistemas contables externos
