-- JARSS - Triggers
-- Base de datos PostgreSQL
-- Fecha: 2025-01-27

-- =====================================================
-- TRIGGERS PARA UPDATED_AT
-- =====================================================

-- Triggers para actualizar updated_at automáticamente
CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_companies_updated_at 
    BEFORE UPDATE ON companies
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_financial_accounts_updated_at 
    BEFORE UPDATE ON financial_accounts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_financial_transactions_updated_at 
    BEFORE UPDATE ON financial_transactions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tax_obligations_updated_at 
    BEFORE UPDATE ON tax_obligations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tax_declarations_updated_at 
    BEFORE UPDATE ON tax_declarations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_employees_updated_at 
    BEFORE UPDATE ON employees
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_payroll_periods_updated_at 
    BEFORE UPDATE ON payroll_periods
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_payroll_entries_updated_at 
    BEFORE UPDATE ON payroll_entries
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_budgets_updated_at 
    BEFORE UPDATE ON budgets
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_budget_lines_updated_at 
    BEFORE UPDATE ON budget_lines
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_suppliers_updated_at 
    BEFORE UPDATE ON suppliers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_customers_updated_at 
    BEFORE UPDATE ON customers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_accounts_payable_updated_at 
    BEFORE UPDATE ON accounts_payable
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_accounts_receivable_updated_at 
    BEFORE UPDATE ON accounts_receivable
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_financial_reports_updated_at 
    BEFORE UPDATE ON financial_reports
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_report_templates_updated_at 
    BEFORE UPDATE ON report_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_investments_updated_at 
    BEFORE UPDATE ON investments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_configurations_updated_at 
    BEFORE UPDATE ON system_configurations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bank_statements_updated_at 
    BEFORE UPDATE ON bank_statements
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_recurring_payments_updated_at 
    BEFORE UPDATE ON recurring_payments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- TRIGGERS PARA LÓGICA DE NEGOCIO
-- =====================================================

-- Trigger para actualizar balance de cuentas
CREATE TRIGGER update_account_balance_trigger
    AFTER INSERT OR UPDATE OR DELETE ON financial_transactions
    FOR EACH ROW EXECUTE FUNCTION update_account_balance();

-- Trigger para calcular varianza presupuestaria
CREATE TRIGGER calculate_budget_variance_trigger
    BEFORE INSERT OR UPDATE ON budget_lines
    FOR EACH ROW EXECUTE FUNCTION update_budget_variance();

-- =====================================================
-- TRIGGERS DE AUDITORÍA
-- =====================================================

-- Triggers de auditoría para tablas críticas
CREATE TRIGGER audit_financial_transactions_trigger
    AFTER INSERT OR UPDATE OR DELETE ON financial_transactions
    FOR EACH ROW EXECUTE FUNCTION audit_trigger();

CREATE TRIGGER audit_payroll_entries_trigger
    AFTER INSERT OR UPDATE OR DELETE ON payroll_entries
    FOR EACH ROW EXECUTE FUNCTION audit_trigger();

CREATE TRIGGER audit_tax_obligations_trigger
    AFTER INSERT OR UPDATE OR DELETE ON tax_obligations
    FOR EACH ROW EXECUTE FUNCTION audit_trigger();

CREATE TRIGGER audit_financial_accounts_trigger
    AFTER INSERT OR UPDATE OR DELETE ON financial_accounts
    FOR EACH ROW EXECUTE FUNCTION audit_trigger();

CREATE TRIGGER audit_employees_trigger
    AFTER INSERT OR UPDATE OR DELETE ON employees
    FOR EACH ROW EXECUTE FUNCTION audit_trigger();

CREATE TRIGGER audit_suppliers_trigger
    AFTER INSERT OR UPDATE OR DELETE ON suppliers
    FOR EACH ROW EXECUTE FUNCTION audit_trigger();

CREATE TRIGGER audit_customers_trigger
    AFTER INSERT OR UPDATE OR DELETE ON customers
    FOR EACH ROW EXECUTE FUNCTION audit_trigger();

-- =====================================================
-- TRIGGERS PARA VALIDACIONES
-- =====================================================

-- Función para validar transacciones
CREATE OR REPLACE FUNCTION validate_transaction()
RETURNS TRIGGER AS $$
BEGIN
    -- Validar que el monto sea positivo
    IF NEW.amount <= 0 THEN
        RAISE EXCEPTION 'El monto de la transacción debe ser mayor a cero';
    END IF;
    
    -- Validar que la fecha no sea futura
    IF NEW.transaction_date > CURRENT_DATE THEN
        RAISE EXCEPTION 'La fecha de transacción no puede ser futura';
    END IF;
    
    -- Validar que la cuenta pertenezca a la misma empresa
    IF NOT EXISTS (
        SELECT 1 FROM financial_accounts 
        WHERE id = NEW.account_id 
        AND company_id = NEW.company_id
        AND is_active = true
    ) THEN
        RAISE EXCEPTION 'La cuenta especificada no existe o no está activa';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger para validar transacciones
CREATE TRIGGER validate_transaction_trigger
    BEFORE INSERT OR UPDATE ON financial_transactions
    FOR EACH ROW EXECUTE FUNCTION validate_transaction();

-- Función para validar empleados
CREATE OR REPLACE FUNCTION validate_employee()
RETURNS TRIGGER AS $$
BEGIN
    -- Validar que el salario sea positivo
    IF NEW.base_salary <= 0 THEN
        RAISE EXCEPTION 'El salario base debe ser mayor a cero';
    END IF;
    
    -- Validar que la fecha de contratación no sea futura
    IF NEW.hire_date > CURRENT_DATE THEN
        RAISE EXCEPTION 'La fecha de contratación no puede ser futura';
    END IF;
    
    -- Validar que la fecha de terminación sea posterior a la contratación
    IF NEW.termination_date IS NOT NULL AND NEW.termination_date <= NEW.hire_date THEN
        RAISE EXCEPTION 'La fecha de terminación debe ser posterior a la fecha de contratación';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger para validar empleados
CREATE TRIGGER validate_employee_trigger
    BEFORE INSERT OR UPDATE ON employees
    FOR EACH ROW EXECUTE FUNCTION validate_employee();

-- Función para validar presupuestos
CREATE OR REPLACE FUNCTION validate_budget_line()
RETURNS TRIGGER AS $$
BEGIN
    -- Validar que el monto presupuestado sea positivo
    IF NEW.budgeted_amount < 0 THEN
        RAISE EXCEPTION 'El monto presupuestado no puede ser negativo';
    END IF;
    
    -- Validar que el mes esté en rango válido
    IF NEW.month < 1 OR NEW.month > 12 THEN
        RAISE EXCEPTION 'El mes debe estar entre 1 y 12';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger para validar líneas presupuestarias
CREATE TRIGGER validate_budget_line_trigger
    BEFORE INSERT OR UPDATE ON budget_lines
    FOR EACH ROW EXECUTE FUNCTION validate_budget_line();

-- =====================================================
-- TRIGGERS PARA NOTIFICACIONES AUTOMÁTICAS
-- =====================================================

-- Función para crear notificaciones automáticas
CREATE OR REPLACE FUNCTION create_automatic_notifications()
RETURNS TRIGGER AS $$
BEGIN
    -- Notificación para obligaciones fiscales próximas a vencer
    IF TG_TABLE_NAME = 'tax_obligations' AND NEW.status = 'pending' THEN
        IF NEW.due_date <= CURRENT_DATE + INTERVAL '7 days' THEN
            INSERT INTO notifications (company_id, title, message, type)
            VALUES (
                NEW.company_id,
                'Obligación Fiscal Próxima a Vencer',
                'La obligación "' || NEW.name || '" vence el ' || NEW.due_date::TEXT,
                'warning'
            );
        END IF;
    END IF;
    
    -- Notificación para transacciones grandes
    IF TG_TABLE_NAME = 'financial_transactions' AND NEW.amount > 100000 THEN
        INSERT INTO notifications (company_id, title, message, type)
        VALUES (
            NEW.company_id,
            'Transacción de Monto Alto',
            'Se registró una transacción de ' || NEW.type || ' por RD$' || NEW.amount::TEXT,
            'info'
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers para notificaciones automáticas
CREATE TRIGGER auto_notifications_tax_obligations
    AFTER INSERT OR UPDATE ON tax_obligations
    FOR EACH ROW EXECUTE FUNCTION create_automatic_notifications();

CREATE TRIGGER auto_notifications_transactions
    AFTER INSERT ON financial_transactions
    FOR EACH ROW EXECUTE FUNCTION create_automatic_notifications();

-- =====================================================
-- TRIGGERS PARA LIMPIEZA AUTOMÁTICA
-- =====================================================

-- Función para limpiar sesiones expiradas
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS TRIGGER AS $$
BEGIN
    -- Marcar sesiones expiradas como inactivas
    UPDATE user_sessions 
    SET is_active = false 
    WHERE expires_at < NOW() AND is_active = true;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger para limpiar sesiones al insertar nuevas
CREATE TRIGGER cleanup_sessions_trigger
    AFTER INSERT ON user_sessions
    FOR EACH STATEMENT EXECUTE FUNCTION cleanup_expired_sessions();

-- =====================================================
-- TRIGGERS PARA CÁLCULOS AUTOMÁTICOS
-- =====================================================

-- Función para calcular totales de nómina
CREATE OR REPLACE FUNCTION update_payroll_totals()
RETURNS TRIGGER AS $$
DECLARE
    period_totals RECORD;
BEGIN
    -- Recalcular totales del período
    SELECT 
        COUNT(*) as employee_count,
        COALESCE(SUM(gross_amount), 0) as total_gross,
        COALESCE(SUM(total_deductions), 0) as total_deductions,
        COALESCE(SUM(net_amount), 0) as total_net
    INTO period_totals
    FROM payroll_entries 
    WHERE period_id = COALESCE(NEW.period_id, OLD.period_id);
    
    -- Actualizar el período
    UPDATE payroll_periods 
    SET 
        employee_count = period_totals.employee_count,
        total_gross = period_totals.total_gross,
        total_deductions = period_totals.total_deductions,
        total_net = period_totals.total_net,
        updated_at = NOW()
    WHERE id = COALESCE(NEW.period_id, OLD.period_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Trigger para actualizar totales de nómina
CREATE TRIGGER update_payroll_totals_trigger
    AFTER INSERT OR UPDATE OR DELETE ON payroll_entries
    FOR EACH ROW EXECUTE FUNCTION update_payroll_totals();
