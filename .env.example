# JARSS - Sistema Integral de Gestión Financiera
# Variables de entorno de ejemplo

# Database Configuration (PostgreSQL)
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=jarss
DATABASE_USER=jarss_user
DATABASE_PASSWORD=secure_password
DATABASE_SSL=false

# Application Configuration
VITE_APP_NAME=JARSS
VITE_APP_VERSION=1.0.0
VITE_APP_ENVIRONMENT=development

# API Configuration
VITE_API_URL=http://localhost:3000/api
API_PORT=3000

# Authentication & Security
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=24h
SESSION_TIMEOUT=3600
MAX_LOGIN_ATTEMPTS=5
BCRYPT_ROUNDS=12

# Dominican Republic Tax Configuration
VITE_ITBIS_RATE=0.18
VITE_AFP_RATE=0.0287
VITE_SFS_RATE=0.0304
VITE_INFOTEP_RATE=0.01

# Banking API Configuration (for future integrations)
VITE_BANKING_API_URL=
VITE_BANKING_API_KEY=

# Email Configuration (for notifications)
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASS=
SMTP_FROM=<EMAIL>

# File Storage Configuration
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=50MB
ALLOWED_FILE_TYPES=pdf,xlsx,csv,jpg,png

# Feature Flags
VITE_ENABLE_BANKING_INTEGRATION=false
VITE_ENABLE_AI_RECOMMENDATIONS=false
VITE_ENABLE_AUTOMATIC_RECONCILIATION=false
VITE_ENABLE_DEMO_DATA=true

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/jarss.log

# CORS Configuration
CORS_ORIGIN=http://localhost:5173
CORS_CREDENTIALS=true
