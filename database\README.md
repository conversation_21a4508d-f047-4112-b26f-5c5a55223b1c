# JARSS - Base de Datos PostgreSQL

## 🏢 Sistema Integral de Gestión Financiera

JARSS es un sistema completo de gestión financiera diseñado específicamente para empresas en República Dominicana, con soporte completo para la normativa fiscal dominicana.

## 🚀 Instalación Rápida

### Requisitos
- PostgreSQL 12 o superior
- Extensiones: `uuid-ossp`, `pgcrypto`

### Instalación Automática
```bash
# Conectar a PostgreSQL
psql -U postgres -d jarss

# Ejecutar instalación completa
\i database/install.sql
```

### Instalación Manual
```bash
# 1. Crear esquema
\i database/schema.sql

# 2. Crear funciones
\i database/functions.sql

# 3. Crear triggers
\i database/triggers.sql

# 4. Insertar datos iniciales
\i database/seed_data.sql
```

## 📊 Estructura de la Base de Datos

### Módulos Principales

#### 👤 **Gestión de Usuarios y Empresas**
- `users` - Usuarios del sistema
- `companies` - Empresas registradas
- `company_users` - Relación usuarios-empresas
- `user_sessions` - Sesiones de autenticación

#### 💰 **Gestión Financiera**
- `financial_accounts` - Cuentas bancarias y financieras
- `financial_transactions` - Transacciones (ingresos/gastos)
- `transaction_categories` - Categorías de transacciones

#### 🇩🇴 **Cumplimiento Fiscal Dominicano**
- `tax_configurations` - Configuración de impuestos (ITBIS, AFP, SFS, INFOTEP)
- `tax_obligations` - Obligaciones fiscales
- `tax_declarations` - Declaraciones DGII

#### 👥 **Gestión de Nómina**
- `employees` - Empleados
- `departments` - Departamentos
- `payroll_periods` - Períodos de nómina
- `payroll_entries` - Entradas de nómina con deducciones dominicanas

#### 📊 **Control Presupuestario**
- `budgets` - Presupuestos anuales
- `budget_categories` - Categorías presupuestarias
- `budget_lines` - Líneas presupuestarias detalladas

#### 🏦 **Bancos y Proveedores**
- `suppliers` - Proveedores
- `customers` - Clientes
- `accounts_payable` - Cuentas por pagar
- `accounts_receivable` - Cuentas por cobrar

#### 📈 **Inversiones**
- `investment_types` - Tipos de inversión
- `investments` - Inversiones
- `investment_movements` - Movimientos de inversiones

#### 📋 **Reportes y Análisis**
- `financial_reports` - Reportes generados
- `report_templates` - Plantillas de reportes

## 🇩🇴 Características Dominicanas

### Impuestos Configurados
- **ITBIS**: 18% (Impuesto sobre Transferencias)
- **AFP**: 2.87% (Fondo de Pensiones)
- **SFS**: 3.04% (Seguro Familiar de Salud)
- **INFOTEP**: 1% (Formación Técnico Profesional)

### Funciones Especializadas
```sql
-- Calcular deducciones dominicanas
SELECT * FROM calculate_dominican_deductions(50000.00, company_id);

-- Calcular ITBIS
SELECT calculate_itbis(1000.00, company_id, false); -- Retorna 180.00
```

## ⚡ Funciones Automáticas

### Actualización de Balances
Los balances de cuentas se actualizan automáticamente al crear/modificar transacciones.

### Cálculos de Nómina
```sql
-- Los cálculos de deducciones se realizan automáticamente
-- basados en las configuraciones fiscales de la empresa
```

### Auditoría Automática
Todas las operaciones críticas se registran automáticamente en `audit_logs`.

### Notificaciones
- Obligaciones fiscales próximas a vencer
- Transacciones de montos altos
- Variaciones presupuestarias

## 🔐 Seguridad

### Autenticación
```sql
-- Hash de contraseñas
SELECT hash_password('mi_contraseña');

-- Verificación de contraseñas
SELECT verify_password('mi_contraseña', hash_almacenado);
```

### Sesiones
Las sesiones se gestionan automáticamente con limpieza de sesiones expiradas.

### Auditoría
Registro completo de cambios en tablas críticas con:
- Usuario que realizó el cambio
- Valores anteriores y nuevos
- Timestamp e IP

## 📈 Uso Básico

### 1. Crear Usuario y Empresa
```sql
-- Crear usuario
INSERT INTO users (email, password_hash, full_name)
VALUES ('<EMAIL>', hash_password('contraseña123'), 'Administrador');

-- Crear empresa
INSERT INTO companies (owner_id, name, rnc, address)
VALUES (user_id, 'Mi Empresa SRL', '101-12345-6', 'Santo Domingo, RD');

-- Configurar datos iniciales
SELECT setup_new_company(company_id);
```

### 2. Registrar Transacciones
```sql
-- Registrar ingreso
INSERT INTO financial_transactions (
    company_id, account_id, category_id, type, amount, description, transaction_date
) VALUES (
    company_id, account_id, category_id, 'ingreso', 25000.00, 
    'Venta de servicios', CURRENT_DATE
);
```

### 3. Gestionar Nómina
```sql
-- Crear empleado
INSERT INTO employees (
    company_id, department_id, employee_code, first_name, last_name, 
    position, hire_date, base_salary
) VALUES (
    company_id, dept_id, 'EMP001', 'Juan', 'Pérez', 
    'Gerente', '2024-01-15', 75000.00
);

-- Calcular deducciones
SELECT * FROM calculate_dominican_deductions(75000.00, company_id);
```

## 🛠️ Mantenimiento

### Respaldos
```sql
-- Respaldar base de datos
pg_dump jarss > backup_jarss_$(date +%Y%m%d).sql

-- Restaurar
psql -U postgres -d jarss_new < backup_jarss_20250127.sql
```

### Limpieza
```sql
-- Limpiar sesiones expiradas (automático)
-- Limpiar notificaciones antiguas
DELETE FROM notifications WHERE created_at < NOW() - INTERVAL '30 days';
```

### Monitoreo
```sql
-- Verificar integridad de balances
SELECT fa.name, fa.balance, 
       COALESCE(SUM(CASE WHEN ft.type = 'ingreso' THEN ft.amount ELSE -ft.amount END), 0) as calculated_balance
FROM financial_accounts fa
LEFT JOIN financial_transactions ft ON fa.id = ft.account_id
WHERE fa.company_id = company_id
GROUP BY fa.id, fa.name, fa.balance;
```

## 📊 Reportes

### Balance General
```sql
SELECT * FROM get_balance_sheet(company_id, CURRENT_DATE);
```

### Estado de Resultados
```sql
-- Ingresos y gastos por período
SELECT 
    tc.name as categoria,
    ft.type,
    SUM(ft.amount) as total
FROM financial_transactions ft
JOIN transaction_categories tc ON ft.category_id = tc.id
WHERE ft.company_id = company_id
AND ft.transaction_date BETWEEN '2024-01-01' AND '2024-12-31'
GROUP BY tc.name, ft.type
ORDER BY ft.type, total DESC;
```

## 🔧 Configuración

### Variables de Entorno Recomendadas
```bash
# PostgreSQL
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=jarss
POSTGRES_USER=jarss_user
POSTGRES_PASSWORD=secure_password

# Aplicación
JARSS_SECRET_KEY=your_secret_key_here
JARSS_SESSION_TIMEOUT=3600
JARSS_MAX_LOGIN_ATTEMPTS=5
```

## 📞 Soporte

### Logs de Error
```sql
-- Verificar logs de auditoría
SELECT * FROM audit_logs 
WHERE created_at > NOW() - INTERVAL '1 day'
ORDER BY created_at DESC;
```

### Diagnóstico
```sql
-- Verificar estado de la base de datos
SELECT 
    schemaname,
    tablename,
    n_tup_ins as inserts,
    n_tup_upd as updates,
    n_tup_del as deletes
FROM pg_stat_user_tables
WHERE schemaname = 'public'
ORDER BY n_tup_ins + n_tup_upd + n_tup_del DESC;
```

## 📚 Documentación Adicional

- `database/API.md` - Documentación detallada de funciones
- `database/schema.sql` - Esquema completo de la base de datos
- `database/functions.sql` - Todas las funciones disponibles
- `database/triggers.sql` - Triggers y validaciones

---

**JARSS** - Sistema Integral de Gestión Financiera para República Dominicana
Desarrollado con ❤️ para empresas dominicanas
