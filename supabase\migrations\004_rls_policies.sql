-- JARSS - Sistema Integral de Gestión Financiera
-- Migración 004: Row Level Security (RLS) Policies
-- Fecha: 2025-01-27

-- =====================================================
-- HAB<PERSON>ITAR RLS EN TODAS LAS TABLAS
-- =====================================================

-- Perfiles
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- <PERSON><PERSON><PERSON><PERSON> Financiero
ALTER TABLE financial_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE transaction_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE financial_transactions ENABLE ROW LEVEL SECURITY;

-- M<PERSON><PERSON><PERSON> Fiscal
ALTER TABLE tax_configurations ENABLE ROW LEVEL SECURITY;
ALTER TABLE tax_obligations ENABLE ROW LEVEL SECURITY;
ALTER TABLE tax_declarations ENABLE ROW LEVEL SECURITY;

-- <PERSON><PERSON><PERSON><PERSON>
ALTER TABLE departments ENABLE ROW LEVEL SECURITY;
ALTER TABLE employees ENABLE ROW LEVEL SECURITY;
ALTER TABLE payroll_periods ENABLE ROW LEVEL SECURITY;
ALTER TABLE payroll_entries ENABLE ROW LEVEL SECURITY;

-- Módulo Presupuestos
ALTER TABLE budgets ENABLE ROW LEVEL SECURITY;
ALTER TABLE budget_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE budget_lines ENABLE ROW LEVEL SECURITY;

-- Módulo Bancos y Proveedores
ALTER TABLE suppliers ENABLE ROW LEVEL SECURITY;
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE accounts_payable ENABLE ROW LEVEL SECURITY;
ALTER TABLE accounts_receivable ENABLE ROW LEVEL SECURITY;

-- Módulo Reportes
ALTER TABLE financial_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE report_templates ENABLE ROW LEVEL SECURITY;

-- Módulo Inversiones
ALTER TABLE investment_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE investments ENABLE ROW LEVEL SECURITY;
ALTER TABLE investment_movements ENABLE ROW LEVEL SECURITY;

-- Módulo Configuración
ALTER TABLE system_configurations ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- Módulo Conciliación
ALTER TABLE bank_statements ENABLE ROW LEVEL SECURITY;
ALTER TABLE bank_movements ENABLE ROW LEVEL SECURITY;

-- Módulo Pagos Programados
ALTER TABLE recurring_payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE scheduled_payment_history ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- POLÍTICAS RLS - PERFILES
-- =====================================================

-- Los usuarios solo pueden ver y editar su propio perfil
CREATE POLICY "Users can view own profile" ON profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- =====================================================
-- POLÍTICAS RLS - MÓDULO FINANCIERO
-- =====================================================

-- Cuentas financieras
CREATE POLICY "Users can manage own financial accounts" ON financial_accounts
    FOR ALL USING (user_id = auth.uid());

-- Categorías de transacciones
CREATE POLICY "Users can manage own transaction categories" ON transaction_categories
    FOR ALL USING (user_id = auth.uid());

-- Transacciones financieras
CREATE POLICY "Users can manage own financial transactions" ON financial_transactions
    FOR ALL USING (user_id = auth.uid());

-- =====================================================
-- POLÍTICAS RLS - MÓDULO FISCAL
-- =====================================================

-- Configuraciones de impuestos
CREATE POLICY "Users can manage own tax configurations" ON tax_configurations
    FOR ALL USING (user_id = auth.uid());

-- Obligaciones fiscales
CREATE POLICY "Users can manage own tax obligations" ON tax_obligations
    FOR ALL USING (user_id = auth.uid());

-- Declaraciones fiscales
CREATE POLICY "Users can manage own tax declarations" ON tax_declarations
    FOR ALL USING (user_id = auth.uid());

-- =====================================================
-- POLÍTICAS RLS - MÓDULO NÓMINA
-- =====================================================

-- Departamentos
CREATE POLICY "Users can manage own departments" ON departments
    FOR ALL USING (user_id = auth.uid());

-- Empleados
CREATE POLICY "Users can manage own employees" ON employees
    FOR ALL USING (user_id = auth.uid());

-- Períodos de nómina
CREATE POLICY "Users can manage own payroll periods" ON payroll_periods
    FOR ALL USING (user_id = auth.uid());

-- Entradas de nómina
CREATE POLICY "Users can manage own payroll entries" ON payroll_entries
    FOR ALL USING (
        period_id IN (
            SELECT id FROM payroll_periods WHERE user_id = auth.uid()
        )
    );

-- =====================================================
-- POLÍTICAS RLS - MÓDULO PRESUPUESTOS
-- =====================================================

-- Presupuestos
CREATE POLICY "Users can manage own budgets" ON budgets
    FOR ALL USING (user_id = auth.uid());

-- Categorías presupuestarias
CREATE POLICY "Users can manage own budget categories" ON budget_categories
    FOR ALL USING (user_id = auth.uid());

-- Líneas presupuestarias
CREATE POLICY "Users can manage own budget lines" ON budget_lines
    FOR ALL USING (
        budget_id IN (
            SELECT id FROM budgets WHERE user_id = auth.uid()
        )
    );

-- =====================================================
-- POLÍTICAS RLS - MÓDULO BANCOS Y PROVEEDORES
-- =====================================================

-- Proveedores
CREATE POLICY "Users can manage own suppliers" ON suppliers
    FOR ALL USING (user_id = auth.uid());

-- Clientes
CREATE POLICY "Users can manage own customers" ON customers
    FOR ALL USING (user_id = auth.uid());

-- Cuentas por pagar
CREATE POLICY "Users can manage own accounts payable" ON accounts_payable
    FOR ALL USING (user_id = auth.uid());

-- Cuentas por cobrar
CREATE POLICY "Users can manage own accounts receivable" ON accounts_receivable
    FOR ALL USING (user_id = auth.uid());

-- =====================================================
-- POLÍTICAS RLS - OTROS MÓDULOS
-- =====================================================

-- Reportes financieros
CREATE POLICY "Users can manage own financial reports" ON financial_reports
    FOR ALL USING (user_id = auth.uid());

-- Plantillas de reportes
CREATE POLICY "Users can manage own report templates" ON report_templates
    FOR ALL USING (user_id = auth.uid());

-- Tipos de inversión
CREATE POLICY "Users can manage own investment types" ON investment_types
    FOR ALL USING (user_id = auth.uid());

-- Inversiones
CREATE POLICY "Users can manage own investments" ON investments
    FOR ALL USING (user_id = auth.uid());

-- Movimientos de inversiones
CREATE POLICY "Users can manage own investment movements" ON investment_movements
    FOR ALL USING (
        investment_id IN (
            SELECT id FROM investments WHERE user_id = auth.uid()
        )
    );

-- Configuraciones del sistema
CREATE POLICY "Users can manage own system configurations" ON system_configurations
    FOR ALL USING (user_id = auth.uid());

-- Notificaciones
CREATE POLICY "Users can manage own notifications" ON notifications
    FOR ALL USING (user_id = auth.uid());

-- Auditoría
CREATE POLICY "Users can view own audit logs" ON audit_logs
    FOR SELECT USING (user_id = auth.uid());

-- Extractos bancarios
CREATE POLICY "Users can manage own bank statements" ON bank_statements
    FOR ALL USING (user_id = auth.uid());

-- Movimientos bancarios
CREATE POLICY "Users can manage own bank movements" ON bank_movements
    FOR ALL USING (
        statement_id IN (
            SELECT id FROM bank_statements WHERE user_id = auth.uid()
        )
    );

-- Pagos recurrentes
CREATE POLICY "Users can manage own recurring payments" ON recurring_payments
    FOR ALL USING (user_id = auth.uid());

-- Historial de pagos programados
CREATE POLICY "Users can manage own scheduled payment history" ON scheduled_payment_history
    FOR ALL USING (
        recurring_payment_id IN (
            SELECT id FROM recurring_payments WHERE user_id = auth.uid()
        )
    );
