-- JARSS - Sistema Integral de Gestión Financiera
-- Migración 006: Datos iniciales (Seed Data)
-- Fecha: 2025-01-27

-- =====================================================
-- CONFIGURACIONES FISCALES DOMINICANAS
-- =====================================================

-- Insertar configuraciones de impuestos dominicanos por defecto
-- Nota: Estos se insertarán para cada usuario cuando se registre

-- =====================================================
-- CATEGORÍAS DE TRANSACCIONES POR DEFECTO
-- =====================================================

-- Función para crear categorías por defecto para un usuario
CREATE OR REPLACE FUNCTION create_default_categories(user_uuid UUID)
RETURNS VOID AS $$
BEGIN
    -- Categorías de Ingresos
    INSERT INTO transaction_categories (user_id, name, type, description) VALUES
    (user_uuid, 'Ventas', 'ingreso', 'Ingresos por ventas de productos o servicios'),
    (user_uuid, 'Servicios', 'ingreso', 'Ingresos por prestación de servicios'),
    (user_uuid, 'Comisiones', 'ingreso', 'Ingresos por comisiones'),
    (user_uuid, 'Intereses', 'ingreso', 'Ingresos por intereses bancarios'),
    (user_uuid, 'Otros Ingresos', 'ingreso', 'Otros tipos de ingresos');

    -- Categorías de Gastos
    INSERT INTO transaction_categories (user_id, name, type, description) VALUES
    (user_uuid, 'Compras', 'gasto', 'Compras de inventario o materias primas'),
    (user_uuid, 'Nómina', 'gasto', 'Gastos de nómina y beneficios'),
    (user_uuid, 'Servicios Públicos', 'gasto', 'Electricidad, agua, teléfono, internet'),
    (user_uuid, 'Alquiler', 'gasto', 'Alquiler de oficinas o locales'),
    (user_uuid, 'Marketing', 'gasto', 'Gastos de publicidad y marketing'),
    (user_uuid, 'Transporte', 'gasto', 'Gastos de transporte y combustible'),
    (user_uuid, 'Suministros de Oficina', 'gasto', 'Papelería y suministros'),
    (user_uuid, 'Mantenimiento', 'gasto', 'Mantenimiento de equipos e instalaciones'),
    (user_uuid, 'Seguros', 'gasto', 'Pólizas de seguros'),
    (user_uuid, 'Impuestos', 'gasto', 'Pagos de impuestos'),
    (user_uuid, 'Otros Gastos', 'gasto', 'Otros tipos de gastos');
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- CONFIGURACIONES FISCALES POR DEFECTO
-- =====================================================

-- Función para crear configuraciones fiscales dominicanas
CREATE OR REPLACE FUNCTION create_default_tax_config(user_uuid UUID)
RETURNS VOID AS $$
BEGIN
    INSERT INTO tax_configurations (user_id, tax_type, rate, description, effective_from) VALUES
    (user_uuid, 'itbis', 0.1800, 'Impuesto sobre Transferencias de Bienes Industrializados y Servicios', '2024-01-01'),
    (user_uuid, 'afp', 0.0287, 'Aporte Fondo de Pensiones', '2024-01-01'),
    (user_uuid, 'sfs', 0.0304, 'Seguro Familiar de Salud', '2024-01-01'),
    (user_uuid, 'infotep', 0.0100, 'Instituto Nacional de Formación Técnico Profesional', '2024-01-01');
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- CATEGORÍAS PRESUPUESTARIAS POR DEFECTO
-- =====================================================

-- Función para crear categorías presupuestarias por defecto
CREATE OR REPLACE FUNCTION create_default_budget_categories(user_uuid UUID)
RETURNS VOID AS $$
BEGIN
    -- Categorías de Ingresos Presupuestarios
    INSERT INTO budget_categories (user_id, name, type, description) VALUES
    (user_uuid, 'Ventas Proyectadas', 'ingreso', 'Proyección de ventas mensuales'),
    (user_uuid, 'Servicios Proyectados', 'ingreso', 'Proyección de ingresos por servicios'),
    (user_uuid, 'Otros Ingresos Proyectados', 'ingreso', 'Otros ingresos esperados');

    -- Categorías de Gastos Presupuestarios
    INSERT INTO budget_categories (user_id, name, type, description) VALUES
    (user_uuid, 'Costo de Ventas', 'gasto', 'Costos directos de productos vendidos'),
    (user_uuid, 'Gastos de Personal', 'gasto', 'Nómina y beneficios del personal'),
    (user_uuid, 'Gastos Operativos', 'gasto', 'Gastos operativos generales'),
    (user_uuid, 'Gastos de Marketing', 'gasto', 'Presupuesto para marketing y publicidad'),
    (user_uuid, 'Gastos Administrativos', 'gasto', 'Gastos administrativos y de oficina');
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- TIPOS DE INVERSIÓN POR DEFECTO
-- =====================================================

-- Función para crear tipos de inversión por defecto
CREATE OR REPLACE FUNCTION create_default_investment_types(user_uuid UUID)
RETURNS VOID AS $$
BEGIN
    INSERT INTO investment_types (user_id, name, description, risk_level) VALUES
    (user_uuid, 'Certificados de Depósito', 'Certificados de depósito bancarios', 'low'),
    (user_uuid, 'Bonos del Estado', 'Bonos emitidos por el gobierno', 'low'),
    (user_uuid, 'Acciones', 'Acciones de empresas públicas', 'high'),
    (user_uuid, 'Fondos Mutuos', 'Fondos de inversión diversificados', 'medium'),
    (user_uuid, 'Bienes Raíces', 'Inversiones en propiedades', 'medium'),
    (user_uuid, 'Otros', 'Otros tipos de inversiones', 'medium');
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- CONFIGURACIONES DEL SISTEMA POR DEFECTO
-- =====================================================

-- Función para crear configuraciones del sistema por defecto
CREATE OR REPLACE FUNCTION create_default_system_config(user_uuid UUID)
RETURNS VOID AS $$
BEGIN
    INSERT INTO system_configurations (user_id, key, value, description, category) VALUES
    (user_uuid, 'default_currency', 'DOP', 'Moneda por defecto del sistema', 'general'),
    (user_uuid, 'date_format', 'DD/MM/YYYY', 'Formato de fecha por defecto', 'general'),
    (user_uuid, 'fiscal_year_start', '01/01', 'Inicio del año fiscal (DD/MM)', 'fiscal'),
    (user_uuid, 'company_name', '', 'Nombre de la empresa', 'company'),
    (user_uuid, 'company_rnc', '', 'RNC de la empresa', 'company'),
    (user_uuid, 'company_address', '', 'Dirección de la empresa', 'company'),
    (user_uuid, 'company_phone', '', 'Teléfono de la empresa', 'company'),
    (user_uuid, 'backup_frequency', 'daily', 'Frecuencia de respaldos automáticos', 'system'),
    (user_uuid, 'notification_email', 'true', 'Enviar notificaciones por email', 'notifications'),
    (user_uuid, 'auto_reconcile', 'false', 'Conciliación bancaria automática', 'banking');
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FUNCIÓN PRINCIPAL PARA SETUP INICIAL
-- =====================================================

-- Función que se ejecuta cuando un usuario se registra
CREATE OR REPLACE FUNCTION setup_new_user()
RETURNS TRIGGER AS $$
BEGIN
    -- Crear categorías por defecto
    PERFORM create_default_categories(NEW.id);
    
    -- Crear configuraciones fiscales
    PERFORM create_default_tax_config(NEW.id);
    
    -- Crear categorías presupuestarias
    PERFORM create_default_budget_categories(NEW.id);
    
    -- Crear tipos de inversión
    PERFORM create_default_investment_types(NEW.id);
    
    -- Crear configuraciones del sistema
    PERFORM create_default_system_config(NEW.id);
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger para setup automático de nuevos usuarios
CREATE TRIGGER setup_new_user_trigger
    AFTER INSERT ON profiles
    FOR EACH ROW EXECUTE FUNCTION setup_new_user();

-- =====================================================
-- DATOS DE EJEMPLO (OPCIONAL)
-- =====================================================

-- Función para crear datos de ejemplo (solo para desarrollo/demo)
CREATE OR REPLACE FUNCTION create_demo_data(user_uuid UUID)
RETURNS VOID AS $$
DECLARE
    account_id UUID;
    budget_id UUID;
    category_id UUID;
BEGIN
    -- Crear cuenta de ejemplo
    INSERT INTO financial_accounts (user_id, name, type, balance, bank_name, account_number)
    VALUES (user_uuid, 'Cuenta Corriente Principal', 'checking', 50000.00, 'Banco Popular', '****1234')
    RETURNING id INTO account_id;

    -- Crear presupuesto de ejemplo para el año actual
    INSERT INTO budgets (user_id, name, year, description, total_income_budget, total_expense_budget)
    VALUES (user_uuid, 'Presupuesto ' || EXTRACT(YEAR FROM CURRENT_DATE), EXTRACT(YEAR FROM CURRENT_DATE), 'Presupuesto anual principal', 600000.00, 450000.00)
    RETURNING id INTO budget_id;

    -- Crear algunas transacciones de ejemplo
    SELECT id INTO category_id FROM transaction_categories WHERE user_id = user_uuid AND name = 'Ventas' LIMIT 1;
    
    INSERT INTO financial_transactions (user_id, account_id, category_id, type, amount, description, transaction_date)
    VALUES 
    (user_uuid, account_id, category_id, 'ingreso', 15000.00, 'Venta de servicios - Cliente ABC', CURRENT_DATE - INTERVAL '5 days'),
    (user_uuid, account_id, category_id, 'ingreso', 8500.00, 'Venta de productos - Cliente XYZ', CURRENT_DATE - INTERVAL '3 days');

    SELECT id INTO category_id FROM transaction_categories WHERE user_id = user_uuid AND name = 'Servicios Públicos' LIMIT 1;
    
    INSERT INTO financial_transactions (user_id, account_id, category_id, type, amount, description, transaction_date)
    VALUES 
    (user_uuid, account_id, category_id, 'gasto', 3500.00, 'Pago de electricidad', CURRENT_DATE - INTERVAL '2 days'),
    (user_uuid, account_id, category_id, 'gasto', 1200.00, 'Pago de internet', CURRENT_DATE - INTERVAL '1 day');

END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- ÍNDICES PARA OPTIMIZACIÓN
-- =====================================================

-- Índices para mejorar el rendimiento de consultas frecuentes
CREATE INDEX idx_financial_transactions_user_date ON financial_transactions(user_id, transaction_date DESC);
CREATE INDEX idx_financial_transactions_account ON financial_transactions(account_id);
CREATE INDEX idx_financial_transactions_category ON financial_transactions(category_id);
CREATE INDEX idx_financial_transactions_type ON financial_transactions(type);

CREATE INDEX idx_payroll_entries_period ON payroll_entries(period_id);
CREATE INDEX idx_payroll_entries_employee ON payroll_entries(employee_id);

CREATE INDEX idx_tax_obligations_user_due ON tax_obligations(user_id, due_date);
CREATE INDEX idx_tax_obligations_status ON tax_obligations(status);

CREATE INDEX idx_budget_lines_budget ON budget_lines(budget_id);
CREATE INDEX idx_budget_lines_category ON budget_lines(category_id);

CREATE INDEX idx_accounts_payable_supplier ON accounts_payable(supplier_id);
CREATE INDEX idx_accounts_receivable_customer ON accounts_receivable(customer_id);

CREATE INDEX idx_notifications_user_read ON notifications(user_id, is_read);
CREATE INDEX idx_audit_logs_user_table ON audit_logs(user_id, table_name);

-- Comentarios en las tablas principales
COMMENT ON TABLE profiles IS 'Perfiles de usuario del sistema JARSS';
COMMENT ON TABLE financial_accounts IS 'Cuentas financieras (bancarias, de inversión, etc.)';
COMMENT ON TABLE financial_transactions IS 'Transacciones financieras (ingresos, gastos, transferencias)';
COMMENT ON TABLE tax_obligations IS 'Obligaciones fiscales y tributarias';
COMMENT ON TABLE employees IS 'Registro de empleados de la empresa';
COMMENT ON TABLE payroll_entries IS 'Entradas de nómina por empleado y período';
COMMENT ON TABLE budgets IS 'Presupuestos anuales de la empresa';
COMMENT ON TABLE suppliers IS 'Proveedores de la empresa';
COMMENT ON TABLE customers IS 'Clientes de la empresa';
