-- JARSS - Sistema Integral de Gestión Financiera
-- Migración 003: Módulos de Reportes e Inversiones
-- Fecha: 2025-01-27

-- =====================================================
-- MÓDULO: REPORTES Y ANÁLISIS FINANCIERO
-- =====================================================

-- Reportes financieros
CREATE TABLE financial_reports (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    type report_type NOT NULL,
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    generation_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status report_status DEFAULT 'draft',
    file_url TEXT,
    data JSONB, -- Almacenar datos del reporte en formato JSON
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Plantillas de reportes personalizados
CREATE TABLE report_templates (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    type report_type NOT NULL,
    configuration JSONB NOT NULL, -- Configuración de campos, filtros, etc.
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- MÓDULO: INVERSIONES
-- =====================================================

-- Tipos de inversión
CREATE TABLE investment_types (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    risk_level TEXT CHECK (risk_level IN ('low', 'medium', 'high')),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Inversiones
CREATE TABLE investments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    investment_type_id UUID REFERENCES investment_types(id) ON DELETE SET NULL,
    name TEXT NOT NULL,
    description TEXT,
    initial_amount DECIMAL(15,2) NOT NULL,
    current_value DECIMAL(15,2) NOT NULL,
    purchase_date DATE NOT NULL,
    maturity_date DATE,
    expected_return_rate DECIMAL(5,4), -- Porcentaje anual esperado
    actual_return_rate DECIMAL(5,4), -- Porcentaje anual real
    broker_name TEXT,
    account_number TEXT,
    notes TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Movimientos de inversiones
CREATE TABLE investment_movements (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    investment_id UUID REFERENCES investments(id) ON DELETE CASCADE NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('buy', 'sell', 'dividend', 'interest', 'fee')),
    amount DECIMAL(15,2) NOT NULL,
    shares DECIMAL(12,6), -- Para acciones
    price_per_share DECIMAL(12,6), -- Para acciones
    movement_date DATE NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- MÓDULO: CONFIGURACIÓN Y PARÁMETROS
-- =====================================================

-- Configuraciones del sistema
CREATE TABLE system_configurations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    key TEXT NOT NULL,
    value TEXT NOT NULL,
    description TEXT,
    category TEXT DEFAULT 'general',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, key)
);

-- Notificaciones del sistema
CREATE TABLE notifications (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    type TEXT DEFAULT 'info' CHECK (type IN ('info', 'warning', 'error', 'success')),
    is_read BOOLEAN DEFAULT false,
    action_url TEXT,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Auditoría de cambios
CREATE TABLE audit_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    table_name TEXT NOT NULL,
    record_id UUID NOT NULL,
    action TEXT NOT NULL CHECK (action IN ('INSERT', 'UPDATE', 'DELETE')),
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- MÓDULO: CONCILIACIÓN BANCARIA
-- =====================================================

-- Extractos bancarios
CREATE TABLE bank_statements (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    account_id UUID REFERENCES financial_accounts(id) ON DELETE CASCADE NOT NULL,
    statement_date DATE NOT NULL,
    opening_balance DECIMAL(15,2) NOT NULL,
    closing_balance DECIMAL(15,2) NOT NULL,
    file_url TEXT,
    is_reconciled BOOLEAN DEFAULT false,
    reconciled_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Movimientos bancarios (del extracto)
CREATE TABLE bank_movements (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    statement_id UUID REFERENCES bank_statements(id) ON DELETE CASCADE NOT NULL,
    transaction_date DATE NOT NULL,
    description TEXT NOT NULL,
    reference_number TEXT,
    amount DECIMAL(15,2) NOT NULL,
    balance DECIMAL(15,2),
    is_matched BOOLEAN DEFAULT false,
    matched_transaction_id UUID REFERENCES financial_transactions(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- MÓDULO: PAGOS PROGRAMADOS
-- =====================================================

-- Pagos recurrentes
CREATE TABLE recurring_payments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    account_id UUID REFERENCES financial_accounts(id) ON DELETE CASCADE NOT NULL,
    supplier_id UUID REFERENCES suppliers(id) ON DELETE SET NULL,
    name TEXT NOT NULL,
    description TEXT,
    amount DECIMAL(15,2) NOT NULL,
    frequency TEXT NOT NULL CHECK (frequency IN ('daily', 'weekly', 'monthly', 'quarterly', 'yearly')),
    start_date DATE NOT NULL,
    end_date DATE,
    next_payment_date DATE NOT NULL,
    last_payment_date DATE,
    is_active BOOLEAN DEFAULT true,
    auto_execute BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Historial de pagos programados
CREATE TABLE scheduled_payment_history (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    recurring_payment_id UUID REFERENCES recurring_payments(id) ON DELETE CASCADE NOT NULL,
    transaction_id UUID REFERENCES financial_transactions(id) ON DELETE SET NULL,
    scheduled_date DATE NOT NULL,
    executed_date DATE,
    amount DECIMAL(15,2) NOT NULL,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'executed', 'failed', 'cancelled')),
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
