import { MainLayout } from "@/components/layout/MainLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Download } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useAsync } from "@/hooks/useAsync";
import { baseApi } from "@/services/api/baseApi";
import { ApiResponse } from "@/types/common";

interface ReportData {
  id: string;
  title: string;
  description: string;
  type: 'balance' | 'resultados' | 'flujo' | 'personalizados';
  status: 'available' | 'coming_soon';
}

const REPORT_TYPES = {
  balance: {
    title: 'Balance General',
    description: 'Estado financiero que muestra activos, pasivos y capital contable'
  },
  resultados: {
    title: 'Estado de Resultados',
    description: 'Informe de ingresos, gastos y beneficios en un período'
  },
  flujo: {
    title: 'Flujo de Efectivo',
    description: 'Análisis del movimiento de efectivo de su empresa'
  },
  personalizados: {
    title: 'Reportes Personalizados',
    description: 'Cree y gestione reportes a su medida'
  }
} as const;

const ReportCard = ({ type, title, description }: { type: keyof typeof REPORT_TYPES; title: string; description: string }) => (
  <Card>
    <CardHeader>
      <CardTitle>{title}</CardTitle>
      <CardDescription>{description}</CardDescription>
    </CardHeader>
    <CardContent>
      <p className="text-center text-gray-500 py-8">
        El reporte {title.toLowerCase()} estará disponible próximamente
      </p>
    </CardContent>
  </Card>
);

const ExportButton = () => (
  <Button variant="outline" className="gap-1">
    <Download className="h-4 w-4" />
    <span>Exportar</span>
  </Button>
);

const Reportes = () => {
  const { execute: fetchReports, state, error } = useAsync<ApiResponse<ReportData[]>>();

  const handleExport = async () => {
    try {
      // Implementar lógica de exportación
      console.log('Exportando reportes...');
    } catch (err) {
      console.error('Error al exportar:', err);
    }
  };

  return (
    <MainLayout>
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">Reportes Financieros</h1>
          <ExportButton />
        </div>

        <Tabs defaultValue="balance">
          <TabsList className="grid w-full grid-cols-4 max-w-2xl">
            {Object.entries(REPORT_TYPES).map(([key, { title }]) => (
              <TabsTrigger key={key} value={key}>
                {title}
              </TabsTrigger>
            ))}
          </TabsList>

          {Object.entries(REPORT_TYPES).map(([key, { title, description }]) => (
            <TabsContent key={key} value={key}>
              <ReportCard type={key as keyof typeof REPORT_TYPES} title={title} description={description} />
            </TabsContent>
          ))}
        </Tabs>

        {error && (
          <div className="text-red-500 text-center">
            Error al cargar los reportes: {error.message}
          </div>
        )}
      </div>
    </MainLayout>
  );
};

export default Reportes;
