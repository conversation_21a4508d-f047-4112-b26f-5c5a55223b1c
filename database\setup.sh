#!/bin/bash

# JARSS - Script de Configuración Rápida
# Sistema Integral de Gestión Financiera
# Fecha: 2025-01-27

set -e

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Función para imprimir mensajes
print_message() {
    echo -e "${BLUE}[JARSS]${NC} $1"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Banner
echo -e "${BLUE}"
echo "  ╔══════════════════════════════════════════════════════════════╗"
echo "  ║                            JARSS                             ║"
echo "  ║              Sistema Integral de Gestión Financiera          ║"
echo "  ║                     Configuración Rápida                     ║"
echo "  ╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

# Variables por defecto
DB_NAME="jarss"
DB_USER="jarss_user"
DB_HOST="localhost"
DB_PORT="5432"

# Verificar si PostgreSQL está instalado
print_message "Verificando PostgreSQL..."
if ! command -v psql &> /dev/null; then
    print_error "PostgreSQL no está instalado. Por favor instálalo primero."
    echo "Ubuntu/Debian: sudo apt-get install postgresql postgresql-contrib"
    echo "CentOS/RHEL: sudo yum install postgresql postgresql-server"
    echo "macOS: brew install postgresql"
    exit 1
fi

print_success "PostgreSQL encontrado"

# Verificar si el servicio está corriendo
if ! pg_isready -h $DB_HOST -p $DB_PORT &> /dev/null; then
    print_warning "PostgreSQL no está corriendo. Intentando iniciar..."
    
    # Intentar iniciar el servicio
    if command -v systemctl &> /dev/null; then
        sudo systemctl start postgresql
    elif command -v service &> /dev/null; then
        sudo service postgresql start
    elif command -v brew &> /dev/null; then
        brew services start postgresql
    else
        print_error "No se pudo iniciar PostgreSQL automáticamente. Inicia el servicio manualmente."
        exit 1
    fi
    
    sleep 2
    
    if ! pg_isready -h $DB_HOST -p $DB_PORT &> /dev/null; then
        print_error "No se pudo conectar a PostgreSQL. Verifica la configuración."
        exit 1
    fi
fi

print_success "PostgreSQL está corriendo"

# Solicitar credenciales
echo
print_message "Configuración de la base de datos:"
read -p "Nombre de la base de datos [$DB_NAME]: " input_db_name
DB_NAME=${input_db_name:-$DB_NAME}

read -p "Usuario de la base de datos [$DB_USER]: " input_db_user
DB_USER=${input_db_user:-$DB_USER}

read -s -p "Contraseña para $DB_USER: " DB_PASSWORD
echo

if [ -z "$DB_PASSWORD" ]; then
    print_error "La contraseña no puede estar vacía"
    exit 1
fi

read -p "Usuario administrador de PostgreSQL [postgres]: " PG_ADMIN_USER
PG_ADMIN_USER=${PG_ADMIN_USER:-postgres}

# Crear base de datos y usuario
print_message "Creando base de datos y usuario..."

# Crear usuario
psql -h $DB_HOST -p $DB_PORT -U $PG_ADMIN_USER -c "CREATE USER $DB_USER WITH PASSWORD '$DB_PASSWORD';" 2>/dev/null || print_warning "El usuario $DB_USER ya existe"

# Crear base de datos
psql -h $DB_HOST -p $DB_PORT -U $PG_ADMIN_USER -c "CREATE DATABASE $DB_NAME OWNER $DB_USER;" 2>/dev/null || print_warning "La base de datos $DB_NAME ya existe"

# Otorgar permisos
psql -h $DB_HOST -p $DB_PORT -U $PG_ADMIN_USER -c "GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;"

print_success "Base de datos y usuario creados"

# Instalar JARSS
print_message "Instalando esquema de JARSS..."

# Verificar que los archivos SQL existen
if [ ! -f "install.sql" ]; then
    print_error "Archivo install.sql no encontrado. Ejecuta este script desde el directorio database/"
    exit 1
fi

# Ejecutar instalación
export PGPASSWORD=$DB_PASSWORD
if psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -f install.sql; then
    print_success "JARSS instalado exitosamente"
else
    print_error "Error durante la instalación"
    exit 1
fi

# Crear archivo .env
print_message "Creando archivo de configuración..."

ENV_FILE="../.env"
if [ -f "$ENV_FILE" ]; then
    print_warning "El archivo .env ya existe. Creando respaldo..."
    cp "$ENV_FILE" "${ENV_FILE}.backup.$(date +%Y%m%d_%H%M%S)"
fi

# Generar JWT secret
JWT_SECRET=$(openssl rand -base64 32 2>/dev/null || echo "change_this_jwt_secret_$(date +%s)")

cat > "$ENV_FILE" << EOF
# JARSS - Sistema Integral de Gestión Financiera
# Archivo de configuración generado automáticamente
# Fecha: $(date)

# Database Configuration (PostgreSQL)
DATABASE_HOST=$DB_HOST
DATABASE_PORT=$DB_PORT
DATABASE_NAME=$DB_NAME
DATABASE_USER=$DB_USER
DATABASE_PASSWORD=$DB_PASSWORD
DATABASE_SSL=false

# Application Configuration
VITE_APP_NAME=JARSS
VITE_APP_VERSION=1.0.0
VITE_APP_ENVIRONMENT=development

# API Configuration
VITE_API_URL=http://localhost:3000/api
API_PORT=3000

# Authentication & Security
JWT_SECRET=$JWT_SECRET
JWT_EXPIRES_IN=24h
SESSION_TIMEOUT=3600
MAX_LOGIN_ATTEMPTS=5
BCRYPT_ROUNDS=12

# Dominican Republic Tax Configuration
VITE_ITBIS_RATE=0.18
VITE_AFP_RATE=0.0287
VITE_SFS_RATE=0.0304
VITE_INFOTEP_RATE=0.01

# Feature Flags
VITE_ENABLE_DEMO_DATA=true

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/jarss.log

# CORS Configuration
CORS_ORIGIN=http://localhost:5173
CORS_CREDENTIALS=true
EOF

print_success "Archivo .env creado"

# Crear datos de demostración (opcional)
echo
read -p "¿Deseas crear datos de demostración? (y/N): " create_demo
if [[ $create_demo =~ ^[Yy]$ ]]; then
    print_message "Creando datos de demostración..."
    
    # Crear empresa y usuario de demo
    psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME << EOF
-- Crear usuario demo
INSERT INTO users (email, password_hash, full_name)
VALUES ('<EMAIL>', hash_password('demo123'), 'Usuario Demo');

-- Crear empresa demo
INSERT INTO companies (owner_id, name, rnc, address)
SELECT id, 'Empresa Demo JARSS', '101-12345-6', 'Santo Domingo, República Dominicana'
FROM users WHERE email = '<EMAIL>';

-- Configurar empresa demo
SELECT setup_new_company(c.id)
FROM companies c
JOIN users u ON c.owner_id = u.id
WHERE u.email = '<EMAIL>';

-- Crear datos de ejemplo
SELECT create_demo_data(c.id)
FROM companies c
JOIN users u ON c.owner_id = u.id
WHERE u.email = '<EMAIL>';
EOF

    print_success "Datos de demostración creados"
    echo
    print_message "Credenciales de demostración:"
    echo "  Email: <EMAIL>"
    echo "  Contraseña: demo123"
fi

# Resumen final
echo
echo -e "${GREEN}🎉 ¡INSTALACIÓN COMPLETADA EXITOSAMENTE! 🎉${NC}"
echo
echo -e "${BLUE}📋 RESUMEN DE CONFIGURACIÓN:${NC}"
echo "  • Base de datos: $DB_NAME"
echo "  • Usuario: $DB_USER"
echo "  • Host: $DB_HOST:$DB_PORT"
echo "  • Archivo de configuración: .env"
echo
echo -e "${BLUE}🚀 PRÓXIMOS PASOS:${NC}"
echo "  1. Configurar el backend de la aplicación"
echo "  2. Instalar dependencias del frontend: npm install"
echo "  3. Iniciar el servidor de desarrollo: npm run dev"
echo "  4. Acceder a http://localhost:5173"
echo
echo -e "${BLUE}📚 DOCUMENTACIÓN:${NC}"
echo "  • README.md - Información general"
echo "  • database/README.md - Documentación de la base de datos"
echo
echo -e "${YELLOW}⚠️  IMPORTANTE:${NC}"
echo "  • Cambia las contraseñas por defecto en producción"
echo "  • Configura HTTPS para producción"
echo "  • Realiza respaldos regulares de la base de datos"
echo

print_success "JARSS está listo para usar!"
