import { BaseEntity } from './common';

export type TransactionType = 'ingreso' | 'gasto' | 'transferencia';
export type TransactionCategory = 'ventas' | 'servicios' | 'comisiones' | 'compras' | 'nomina' | 'impuestos';

export interface Transaction extends BaseEntity {
  type: TransactionType;
  amount: number;
  description: string;
  date: string;
  category: TransactionCategory;
  account_id: string | null;
}

export interface Account extends BaseEntity {
  name: string;
  type: 'checking' | 'savings' | 'credit' | 'investment';
  balance: number;
  currency: string;
  is_active: boolean;
}

export interface FinancialSummary {
  totalBalance: number;
  monthlyIncome: number;
  monthlyExpenses: number;
  accountsReceivable: number;
  trends: {
    balance: number;
    income: number;
    expenses: number;
  };
}

export interface TransactionFilters {
  type?: TransactionType;
  category?: TransactionCategory;
  startDate?: string;
  endDate?: string;
  accountId?: string;
  search?: string;
} 