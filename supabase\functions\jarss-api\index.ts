// JARSS API - Edge Function para funcionalidades específicas del sistema
import { serve } from "https://deno.land/std@0.177.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.7.1";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
  "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
};

interface DominicanTaxCalculation {
  grossSalary: number;
  afpDeduction: number;
  sfsDeduction: number;
  infotepDeduction: number;
  isrDeduction: number;
  totalDeductions: number;
  netSalary: number;
}

interface ITBISCalculation {
  baseAmount: number;
  itbisAmount: number;
  totalAmount: number;
  rate: number;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_ANON_KEY") ?? "",
      {
        global: {
          headers: { Authorization: req.headers.get("Authorization")! },
        },
      }
    );

    // Verificar autenticación
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser();
    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: "Unauthorized access" }),
        { status: 401, headers: { ...corsHeaders, "Content-Type": "application/json" } }
      );
    }

    const url = new URL(req.url);
    const action = url.searchParams.get("action");

    switch (action) {
      case "calculate_dominican_taxes":
        return await calculateDominicanTaxes(req, supabaseClient, user.id);
      
      case "calculate_itbis":
        return await calculateITBIS(req, supabaseClient, user.id);
      
      case "generate_payroll":
        return await generatePayroll(req, supabaseClient, user.id);
      
      case "update_budget_actuals":
        return await updateBudgetActuals(req, supabaseClient, user.id);
      
      case "reconcile_bank_statement":
        return await reconcileBankStatement(req, supabaseClient, user.id);
      
      case "generate_financial_report":
        return await generateFinancialReport(req, supabaseClient, user.id);
      
      default:
        return new Response(
          JSON.stringify({ error: "Invalid action" }),
          { status: 400, headers: { ...corsHeaders, "Content-Type": "application/json" } }
        );
    }

  } catch (error) {
    console.error("JARSS API Error:", error);
    return new Response(
      JSON.stringify({ error: "Internal server error" }),
      { status: 500, headers: { ...corsHeaders, "Content-Type": "application/json" } }
    );
  }
});

// Calcular impuestos dominicanos para nómina
async function calculateDominicanTaxes(req: Request, supabase: any, userId: string) {
  const { grossSalary } = await req.json();

  // Obtener configuraciones de impuestos del usuario
  const { data: taxConfigs, error } = await supabase
    .from('tax_configurations')
    .select('*')
    .eq('user_id', userId)
    .eq('is_active', true);

  if (error) {
    return new Response(
      JSON.stringify({ error: "Error fetching tax configurations" }),
      { status: 500, headers: { ...corsHeaders, "Content-Type": "application/json" } }
    );
  }

  // Tasas por defecto (República Dominicana 2024)
  const afpRate = taxConfigs?.find(t => t.tax_type === 'afp')?.rate || 0.0287;
  const sfsRate = taxConfigs?.find(t => t.tax_type === 'sfs')?.rate || 0.0304;
  const infotepRate = taxConfigs?.find(t => t.tax_type === 'infotep')?.rate || 0.01;

  // Calcular deducciones
  const afpDeduction = Math.round(grossSalary * afpRate * 100) / 100;
  const sfsDeduction = Math.round(grossSalary * sfsRate * 100) / 100;
  const infotepDeduction = Math.round(grossSalary * infotepRate * 100) / 100;

  // Calcular ISR (simplificado)
  const isrExemptAnnual = 416220; // Exención anual 2024
  const monthlyExempt = isrExemptAnnual / 12;
  const taxableIncome = grossSalary - afpDeduction - sfsDeduction;
  
  let isrDeduction = 0;
  if (taxableIncome > monthlyExempt) {
    isrDeduction = Math.round((taxableIncome - monthlyExempt) * 0.15 * 100) / 100;
  }

  const totalDeductions = afpDeduction + sfsDeduction + infotepDeduction + isrDeduction;
  const netSalary = grossSalary - totalDeductions;

  const result: DominicanTaxCalculation = {
    grossSalary,
    afpDeduction,
    sfsDeduction,
    infotepDeduction,
    isrDeduction,
    totalDeductions,
    netSalary
  };

  return new Response(
    JSON.stringify(result),
    { headers: { ...corsHeaders, "Content-Type": "application/json" } }
  );
}

// Calcular ITBIS
async function calculateITBIS(req: Request, supabase: any, userId: string) {
  const { baseAmount, isExempt = false } = await req.json();

  if (isExempt) {
    const result: ITBISCalculation = {
      baseAmount,
      itbisAmount: 0,
      totalAmount: baseAmount,
      rate: 0
    };

    return new Response(
      JSON.stringify(result),
      { headers: { ...corsHeaders, "Content-Type": "application/json" } }
    );
  }

  // Obtener tasa de ITBIS del usuario
  const { data: itbisConfig } = await supabase
    .from('tax_configurations')
    .select('rate')
    .eq('user_id', userId)
    .eq('tax_type', 'itbis')
    .eq('is_active', true)
    .single();

  const itbisRate = itbisConfig?.rate || 0.18; // 18% por defecto
  const itbisAmount = Math.round(baseAmount * itbisRate * 100) / 100;
  const totalAmount = baseAmount + itbisAmount;

  const result: ITBISCalculation = {
    baseAmount,
    itbisAmount,
    totalAmount,
    rate: itbisRate
  };

  return new Response(
    JSON.stringify(result),
    { headers: { ...corsHeaders, "Content-Type": "application/json" } }
  );
}

// Generar nómina para un período
async function generatePayroll(req: Request, supabase: any, userId: string) {
  const { periodId } = await req.json();

  // Obtener empleados activos
  const { data: employees, error: empError } = await supabase
    .from('employees')
    .select('*')
    .eq('user_id', userId)
    .eq('status', 'active');

  if (empError) {
    return new Response(
      JSON.stringify({ error: "Error fetching employees" }),
      { status: 500, headers: { ...corsHeaders, "Content-Type": "application/json" } }
    );
  }

  const payrollEntries = [];
  let totalGross = 0;
  let totalDeductions = 0;
  let totalNet = 0;

  // Procesar cada empleado
  for (const employee of employees || []) {
    // Calcular impuestos dominicanos
    const taxCalc = await calculateDominicanTaxesInternal(employee.base_salary, supabase, userId);
    
    const entry = {
      period_id: periodId,
      employee_id: employee.id,
      days_worked: 30,
      base_salary: employee.base_salary,
      overtime_amount: 0,
      bonuses: 0,
      commissions: 0,
      afp_deduction: taxCalc.afpDeduction,
      sfs_deduction: taxCalc.sfsDeduction,
      infotep_deduction: taxCalc.infotepDeduction,
      isr_deduction: taxCalc.isrDeduction,
      other_deductions: 0,
      gross_amount: taxCalc.grossSalary,
      total_deductions: taxCalc.totalDeductions,
      net_amount: taxCalc.netSalary,
      status: 'pending'
    };

    payrollEntries.push(entry);
    totalGross += taxCalc.grossSalary;
    totalDeductions += taxCalc.totalDeductions;
    totalNet += taxCalc.netSalary;
  }

  // Insertar entradas de nómina
  const { error: insertError } = await supabase
    .from('payroll_entries')
    .insert(payrollEntries);

  if (insertError) {
    return new Response(
      JSON.stringify({ error: "Error creating payroll entries" }),
      { status: 500, headers: { ...corsHeaders, "Content-Type": "application/json" } }
    );
  }

  // Actualizar totales del período
  const { error: updateError } = await supabase
    .from('payroll_periods')
    .update({
      total_gross: totalGross,
      total_deductions: totalDeductions,
      total_net: totalNet,
      employee_count: employees?.length || 0,
      status: 'processing'
    })
    .eq('id', periodId);

  if (updateError) {
    return new Response(
      JSON.stringify({ error: "Error updating payroll period" }),
      { status: 500, headers: { ...corsHeaders, "Content-Type": "application/json" } }
    );
  }

  return new Response(
    JSON.stringify({
      success: true,
      message: "Payroll generated successfully",
      summary: {
        employees: employees?.length || 0,
        totalGross,
        totalDeductions,
        totalNet
      }
    }),
    { headers: { ...corsHeaders, "Content-Type": "application/json" } }
  );
}

// Función auxiliar para cálculos internos
async function calculateDominicanTaxesInternal(grossSalary: number, supabase: any, userId: string) {
  const { data: taxConfigs } = await supabase
    .from('tax_configurations')
    .select('*')
    .eq('user_id', userId)
    .eq('is_active', true);

  const afpRate = taxConfigs?.find(t => t.tax_type === 'afp')?.rate || 0.0287;
  const sfsRate = taxConfigs?.find(t => t.tax_type === 'sfs')?.rate || 0.0304;
  const infotepRate = taxConfigs?.find(t => t.tax_type === 'infotep')?.rate || 0.01;

  const afpDeduction = Math.round(grossSalary * afpRate * 100) / 100;
  const sfsDeduction = Math.round(grossSalary * sfsRate * 100) / 100;
  const infotepDeduction = Math.round(grossSalary * infotepRate * 100) / 100;

  const isrExemptAnnual = 416220;
  const monthlyExempt = isrExemptAnnual / 12;
  const taxableIncome = grossSalary - afpDeduction - sfsDeduction;
  
  let isrDeduction = 0;
  if (taxableIncome > monthlyExempt) {
    isrDeduction = Math.round((taxableIncome - monthlyExempt) * 0.15 * 100) / 100;
  }

  const totalDeductions = afpDeduction + sfsDeduction + infotepDeduction + isrDeduction;
  const netSalary = grossSalary - totalDeductions;

  return {
    grossSalary,
    afpDeduction,
    sfsDeduction,
    infotepDeduction,
    isrDeduction,
    totalDeductions,
    netSalary
  };
}

// Actualizar montos reales del presupuesto
async function updateBudgetActuals(req: Request, supabase: any, userId: string) {
  const { budgetId, year, month } = await req.json();

  // Obtener transacciones del mes
  const startDate = `${year}-${month.toString().padStart(2, '0')}-01`;
  const endDate = new Date(year, month, 0).toISOString().split('T')[0];

  const { data: transactions, error } = await supabase
    .from('financial_transactions')
    .select('type, amount, category_id')
    .eq('user_id', userId)
    .gte('transaction_date', startDate)
    .lte('transaction_date', endDate);

  if (error) {
    return new Response(
      JSON.stringify({ error: "Error fetching transactions" }),
      { status: 500, headers: { ...corsHeaders, "Content-Type": "application/json" } }
    );
  }

  // Agrupar por categoría y tipo
  const categoryTotals = {};
  for (const tx of transactions || []) {
    if (!categoryTotals[tx.category_id]) {
      categoryTotals[tx.category_id] = { ingreso: 0, gasto: 0 };
    }
    categoryTotals[tx.category_id][tx.type] += tx.amount;
  }

  // Actualizar líneas presupuestarias
  for (const [categoryId, totals] of Object.entries(categoryTotals)) {
    const actualAmount = totals.ingreso - totals.gasto;
    
    await supabase
      .from('budget_lines')
      .update({ actual_amount: actualAmount })
      .eq('budget_id', budgetId)
      .eq('category_id', categoryId)
      .eq('month', month);
  }

  return new Response(
    JSON.stringify({ success: true, message: "Budget actuals updated" }),
    { headers: { ...corsHeaders, "Content-Type": "application/json" } }
  );
}

// Placeholder para otras funciones
async function reconcileBankStatement(req: Request, supabase: any, userId: string) {
  return new Response(
    JSON.stringify({ message: "Bank reconciliation feature coming soon" }),
    { headers: { ...corsHeaders, "Content-Type": "application/json" } }
  );
}

async function generateFinancialReport(req: Request, supabase: any, userId: string) {
  return new Response(
    JSON.stringify({ message: "Financial report generation feature coming soon" }),
    { headers: { ...corsHeaders, "Content-Type": "application/json" } }
  );
}
