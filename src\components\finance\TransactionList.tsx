import { useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowUp, ArrowDown } from "lucide-react";
import { Transaction } from '@/types/finance';
import { useAsync } from '@/hooks/useAsync';
import { financeService } from '@/services/finance/financeService';

interface TransactionListProps {
  refreshTrigger?: number;
}

export const TransactionList = ({ refreshTrigger = 0 }: TransactionListProps) => {
  const { execute: fetchTransactions, data, state, error } = useAsync();

  useEffect(() => {
    fetchTransactions(financeService.getTransactions());
  }, [refreshTrigger]);

  if (state === 'loading') {
    return (
      <Card className="glass-morphism">
        <CardContent className="py-8">
          <p className="text-center text-gray-500">Cargando transacciones...</p>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="glass-morphism">
        <CardContent className="py-8">
          <p className="text-center text-red-500">
            Error al cargar las transacciones: {error.message}
          </p>
        </CardContent>
      </Card>
    );
  }

  const transactions = data?.data || [];

  return (
    <Card className="glass-morphism">
      <CardHeader>
        <CardTitle>Últimas Transacciones</CardTitle>
        <CardDescription>
          Movimientos financieros recientes
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {transactions.map((transaction) => (
            <TransactionItem key={transaction.id} transaction={transaction} />
          ))}
          {transactions.length === 0 && (
            <p className="text-center text-gray-500 py-4">
              No hay transacciones para mostrar
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

interface TransactionItemProps {
  transaction: Transaction;
}

const TransactionItem = ({ transaction }: TransactionItemProps) => {
  const isIncome = transaction.type === 'ingreso';
  const Icon = isIncome ? ArrowUp : ArrowDown;
  const amountColor = isIncome ? 'text-green-600' : 'text-red-600';
  const amountPrefix = isIncome ? '+' : '-';

  return (
    <div className="flex justify-between items-center p-3 bg-white/10 rounded-lg border border-gray-200/10">
      <div className="flex items-center gap-3">
        <div className={`h-8 w-8 rounded-full ${isIncome ? 'bg-green-100' : 'bg-red-100'} flex items-center justify-center`}>
          <Icon className={`h-4 w-4 ${isIncome ? 'text-green-600' : 'text-red-600'}`} />
        </div>
        <div>
          <p className="font-medium">{transaction.description}</p>
          <p className="text-sm text-gray-500">{transaction.category}</p>
        </div>
      </div>
      <div className="text-right">
        <p className={`font-medium ${amountColor}`}>
          {amountPrefix}${transaction.amount.toFixed(2)}
        </p>
        <p className="text-sm text-gray-500">
          {new Date(transaction.date).toLocaleDateString()}
        </p>
      </div>
    </div>
  );
}; 