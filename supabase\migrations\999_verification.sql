-- JARSS - Script de Verificación de Base de Datos
-- Ejecutar después de todas las migraciones para verificar que todo funciona correctamente

-- =====================================================
-- VERIFICACIÓN DE TABLAS
-- =====================================================

DO $$
DECLARE
    table_count INTEGER;
    expected_tables TEXT[] := ARRAY[
        'profiles', 'financial_accounts', 'transaction_categories', 'financial_transactions',
        'tax_configurations', 'tax_obligations', 'tax_declarations',
        'departments', 'employees', 'payroll_periods', 'payroll_entries',
        'budgets', 'budget_categories', 'budget_lines',
        'suppliers', 'customers', 'accounts_payable', 'accounts_receivable',
        'financial_reports', 'report_templates',
        'investment_types', 'investments', 'investment_movements',
        'system_configurations', 'notifications', 'audit_logs',
        'bank_statements', 'bank_movements',
        'recurring_payments', 'scheduled_payment_history'
    ];
    missing_tables TEXT[] := '{}';
    table_name TEXT;
BEGIN
    RAISE NOTICE '🔍 VERIFICANDO TABLAS...';
    
    -- Verificar que todas las tablas esperadas existen
    FOREACH table_name IN ARRAY expected_tables
    LOOP
        SELECT COUNT(*) INTO table_count
        FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_name = table_name;
        
        IF table_count = 0 THEN
            missing_tables := array_append(missing_tables, table_name);
        END IF;
    END LOOP;
    
    IF array_length(missing_tables, 1) > 0 THEN
        RAISE EXCEPTION '❌ TABLAS FALTANTES: %', array_to_string(missing_tables, ', ');
    ELSE
        RAISE NOTICE '✅ Todas las tablas creadas correctamente (%)', array_length(expected_tables, 1);
    END IF;
END $$;

-- =====================================================
-- VERIFICACIÓN DE RLS
-- =====================================================

DO $$
DECLARE
    rls_count INTEGER;
    total_tables INTEGER;
BEGIN
    RAISE NOTICE '🔒 VERIFICANDO ROW LEVEL SECURITY...';
    
    SELECT COUNT(*) INTO total_tables
    FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name NOT LIKE 'pg_%'
    AND table_type = 'BASE TABLE';
    
    SELECT COUNT(*) INTO rls_count
    FROM pg_tables 
    WHERE schemaname = 'public' 
    AND rowsecurity = true;
    
    IF rls_count < total_tables THEN
        RAISE WARNING '⚠️  RLS no está habilitado en todas las tablas (%/%)', rls_count, total_tables;
    ELSE
        RAISE NOTICE '✅ RLS habilitado en todas las tablas (%)', rls_count;
    END IF;
END $$;

-- =====================================================
-- VERIFICACIÓN DE FUNCIONES
-- =====================================================

DO $$
DECLARE
    function_count INTEGER;
    expected_functions TEXT[] := ARRAY[
        'update_updated_at_column',
        'calculate_dominican_deductions',
        'calculate_itbis',
        'update_account_balance',
        'audit_trigger',
        'update_budget_variance',
        'setup_new_user',
        'create_default_categories',
        'create_default_tax_config',
        'create_default_budget_categories',
        'create_default_investment_types',
        'create_default_system_config',
        'create_demo_data'
    ];
    missing_functions TEXT[] := '{}';
    function_name TEXT;
BEGIN
    RAISE NOTICE '⚙️  VERIFICANDO FUNCIONES...';
    
    FOREACH function_name IN ARRAY expected_functions
    LOOP
        SELECT COUNT(*) INTO function_count
        FROM information_schema.routines 
        WHERE routine_schema = 'public' 
        AND routine_name = function_name;
        
        IF function_count = 0 THEN
            missing_functions := array_append(missing_functions, function_name);
        END IF;
    END LOOP;
    
    IF array_length(missing_functions, 1) > 0 THEN
        RAISE WARNING '⚠️  FUNCIONES FALTANTES: %', array_to_string(missing_functions, ', ');
    ELSE
        RAISE NOTICE '✅ Todas las funciones creadas correctamente (%)', array_length(expected_functions, 1);
    END IF;
END $$;

-- =====================================================
-- VERIFICACIÓN DE TRIGGERS
-- =====================================================

DO $$
DECLARE
    trigger_count INTEGER;
BEGIN
    RAISE NOTICE '🔄 VERIFICANDO TRIGGERS...';
    
    SELECT COUNT(*) INTO trigger_count
    FROM information_schema.triggers 
    WHERE trigger_schema = 'public';
    
    IF trigger_count < 10 THEN
        RAISE WARNING '⚠️  Pocos triggers encontrados (%)', trigger_count;
    ELSE
        RAISE NOTICE '✅ Triggers creados correctamente (%)', trigger_count;
    END IF;
END $$;

-- =====================================================
-- VERIFICACIÓN DE TIPOS ENUMERADOS
-- =====================================================

DO $$
DECLARE
    enum_count INTEGER;
    expected_enums TEXT[] := ARRAY[
        'transaction_type', 'transaction_status', 'account_type',
        'employee_status', 'payroll_status', 'tax_type', 'tax_status',
        'report_type', 'report_status'
    ];
    missing_enums TEXT[] := '{}';
    enum_name TEXT;
BEGIN
    RAISE NOTICE '📝 VERIFICANDO TIPOS ENUMERADOS...';
    
    FOREACH enum_name IN ARRAY expected_enums
    LOOP
        SELECT COUNT(*) INTO enum_count
        FROM pg_type 
        WHERE typname = enum_name;
        
        IF enum_count = 0 THEN
            missing_enums := array_append(missing_enums, enum_name);
        END IF;
    END LOOP;
    
    IF array_length(missing_enums, 1) > 0 THEN
        RAISE WARNING '⚠️  TIPOS ENUMERADOS FALTANTES: %', array_to_string(missing_enums, ', ');
    ELSE
        RAISE NOTICE '✅ Todos los tipos enumerados creados correctamente (%)', array_length(expected_enums, 1);
    END IF;
END $$;

-- =====================================================
-- VERIFICACIÓN DE ÍNDICES
-- =====================================================

DO $$
DECLARE
    index_count INTEGER;
BEGIN
    RAISE NOTICE '📊 VERIFICANDO ÍNDICES...';
    
    SELECT COUNT(*) INTO index_count
    FROM pg_indexes 
    WHERE schemaname = 'public'
    AND indexname LIKE 'idx_%';
    
    IF index_count < 5 THEN
        RAISE WARNING '⚠️  Pocos índices personalizados encontrados (%)', index_count;
    ELSE
        RAISE NOTICE '✅ Índices creados correctamente (%)', index_count;
    END IF;
END $$;

-- =====================================================
-- PRUEBA DE FUNCIONES CRÍTICAS
-- =====================================================

DO $$
DECLARE
    test_result RECORD;
BEGIN
    RAISE NOTICE '🧪 PROBANDO FUNCIONES CRÍTICAS...';
    
    -- Probar cálculo de deducciones dominicanas
    SELECT * INTO test_result FROM calculate_dominican_deductions(50000.00);
    
    IF test_result.afp_amount > 0 AND test_result.sfs_amount > 0 THEN
        RAISE NOTICE '✅ Función de deducciones dominicanas funciona correctamente';
    ELSE
        RAISE WARNING '⚠️  Problema con función de deducciones dominicanas';
    END IF;
    
    -- Probar cálculo de ITBIS
    IF calculate_itbis(1000.00, false) = 180.00 THEN
        RAISE NOTICE '✅ Función de cálculo ITBIS funciona correctamente';
    ELSE
        RAISE WARNING '⚠️  Problema con función de cálculo ITBIS';
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE WARNING '⚠️  Error al probar funciones: %', SQLERRM;
END $$;

-- =====================================================
-- VERIFICACIÓN DE CONFIGURACIONES POR DEFECTO
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '⚙️  VERIFICANDO CONFIGURACIONES POR DEFECTO...';
    
    -- Las configuraciones por defecto se crean cuando se registra un usuario
    -- Esta verificación se puede hacer después de crear el primer usuario
    
    RAISE NOTICE '✅ Sistema listo para crear configuraciones por defecto al registrar usuarios';
END $$;

-- =====================================================
-- RESUMEN FINAL
-- =====================================================

DO $$
DECLARE
    table_count INTEGER;
    function_count INTEGER;
    trigger_count INTEGER;
    policy_count INTEGER;
BEGIN
    -- Contar elementos creados
    SELECT COUNT(*) INTO table_count
    FROM information_schema.tables 
    WHERE table_schema = 'public' AND table_type = 'BASE TABLE';
    
    SELECT COUNT(*) INTO function_count
    FROM information_schema.routines 
    WHERE routine_schema = 'public';
    
    SELECT COUNT(*) INTO trigger_count
    FROM information_schema.triggers 
    WHERE trigger_schema = 'public';
    
    SELECT COUNT(*) INTO policy_count
    FROM pg_policies 
    WHERE schemaname = 'public';
    
    RAISE NOTICE '';
    RAISE NOTICE '🎉 ===== RESUMEN DE VERIFICACIÓN =====';
    RAISE NOTICE '📊 Tablas creadas: %', table_count;
    RAISE NOTICE '⚙️  Funciones creadas: %', function_count;
    RAISE NOTICE '🔄 Triggers creados: %', trigger_count;
    RAISE NOTICE '🔒 Políticas RLS creadas: %', policy_count;
    RAISE NOTICE '';
    RAISE NOTICE '✅ JARSS DATABASE SETUP COMPLETADO EXITOSAMENTE!';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 El sistema está listo para:';
    RAISE NOTICE '   • Registro de usuarios';
    RAISE NOTICE '   • Gestión financiera completa';
    RAISE NOTICE '   • Cumplimiento fiscal dominicano';
    RAISE NOTICE '   • Gestión de nómina';
    RAISE NOTICE '   • Control presupuestario';
    RAISE NOTICE '   • Gestión de proveedores y clientes';
    RAISE NOTICE '   • Reportes financieros';
    RAISE NOTICE '   • Inversiones';
    RAISE NOTICE '';
    RAISE NOTICE '📚 Consulta DATABASE_SCHEMA.md para documentación completa';
    RAISE NOTICE '🔧 Consulta SETUP_INSTRUCTIONS.md para configuración del frontend';
    RAISE NOTICE '';
END $$;
