import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar } from "lucide-react";
import { Transaction, TransactionCategory, TransactionType } from '@/types/finance';
import { useAsync } from '@/hooks/useAsync';
import { financeService } from '@/services/finance/financeService';
import { toast } from '@/components/ui/use-toast';

interface TransactionFormProps {
  onTransactionCreated?: () => void;
}

const TRANSACTION_TYPES: TransactionType[] = ['ingreso', 'gasto', 'transferencia'];
const TRANSACTION_CATEGORIES: TransactionCategory[] = [
  'ventas', 'servicios', 'comisiones', 'compras', 'nomina', 'impuestos'
];

export const TransactionForm = ({ onTransactionCreated }: TransactionFormProps) => {
  const [formData, setFormData] = useState({
    type: 'ingreso' as TransactionType,
    amount: '',
    category: '' as TransactionCategory,
    description: '',
    date: '',
    account_id: '',
  });

  const { execute: createTransaction, state } = useAsync();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      await createTransaction(
        financeService.createTransaction({
          ...formData,
          amount: parseFloat(formData.amount),
          date: formData.date || new Date().toISOString(),
        })
      );

      toast({
        title: "Transacción registrada",
        description: "La transacción ha sido registrada exitosamente",
      });

      // Reset form
      setFormData({
        type: 'ingreso',
        amount: '',
        category: '' as TransactionCategory,
        description: '',
        date: '',
        account_id: '',
      });

      onTransactionCreated?.();
    } catch (error) {
      toast({
        title: "Error",
        description: "No se pudo registrar la transacción",
        variant: "destructive",
      });
    }
  };

  const handleChange = (field: keyof typeof formData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <Card className="glass-morphism">
      <CardHeader>
        <CardTitle>Registrar Transacción</CardTitle>
        <CardDescription>
          Ingrese los detalles de la transacción
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="tipo">Tipo de Transacción</Label>
            <Select 
              value={formData.type} 
              onValueChange={(value) => handleChange('type', value as TransactionType)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Seleccione tipo" />
              </SelectTrigger>
              <SelectContent>
                {TRANSACTION_TYPES.map(type => (
                  <SelectItem key={type} value={type}>
                    {type.charAt(0).toUpperCase() + type.slice(1)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="amount">Monto</Label>
            <Input 
              id="amount" 
              placeholder="0.00" 
              type="number" 
              step="0.01"
              value={formData.amount}
              onChange={(e) => handleChange('amount', e.target.value)}
              required
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="category">Categoría</Label>
            <Select 
              value={formData.category} 
              onValueChange={(value) => handleChange('category', value as TransactionCategory)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Seleccione categoría" />
              </SelectTrigger>
              <SelectContent>
                {TRANSACTION_CATEGORIES.map(category => (
                  <SelectItem key={category} value={category}>
                    {category.charAt(0).toUpperCase() + category.slice(1)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="description">Descripción</Label>
            <Input 
              id="description" 
              placeholder="Descripción de la transacción" 
              value={formData.description}
              onChange={(e) => handleChange('description', e.target.value)}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="date">Fecha</Label>
            <div className="relative">
              <Input 
                id="date" 
                type="date" 
                value={formData.date}
                onChange={(e) => handleChange('date', e.target.value)}
              />
              <Calendar className="absolute right-3 top-2.5 h-4 w-4 text-gray-500" />
            </div>
          </div>
          
          <Button 
            type="submit" 
            className="w-full"
            disabled={state === 'loading'}
          >
            {state === 'loading' ? 'Registrando...' : 'Registrar Transacción'}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}; 